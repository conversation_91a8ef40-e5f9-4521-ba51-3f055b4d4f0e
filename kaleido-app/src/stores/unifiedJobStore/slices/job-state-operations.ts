// ============================================================================
// JOB STATE OPERATIONS SLICE - Array Management and State Tracking
// ============================================================================
// This slice handles jobStateStore compatibility with array-based job management

// No additional imports needed for this slice

export interface JobStateOperationsSlice {
  // ========== JobStateStore Compatibility Methods ==========
  setJobs: (jobs: any[]) => void;
  addJob: (job: any) => void;
  updateJob: (jobId: string, updates: any) => void;
  removeJob: (jobId: string) => void;
  setSelectedJob: (job: any | null) => void;
  refreshJob: (jobId: string, updatedJob: any) => void;
  markJobAsUpdated: (jobId: string) => void;
  isJobRecentlyUpdated: (jobId: string, thresholdMs?: number) => boolean;
  getJobById: (jobId: string) => any | undefined;
  updateMultipleJobs: (updates: Array<{ jobId: string; updates: any }>) => void;

  // ========== Cache and Utility Methods ==========
  clearCache: () => void;
  invalidateCache: (key?: string) => void;
  shouldRefetch: (key: string) => boolean;
  isDataStale: (maxAgeMs?: number) => boolean;

  // ========== Store Management ==========
  reset: () => void;
  resetStore: () => void;
}

export const createJobStateOperationsSlice = (set: any, get: any): JobStateOperationsSlice => ({
  // ========== JobStateStore Compatibility Methods ==========
  setJobs: (jobs: any[]) => {
    const jobsById: any = {};
    jobs.forEach(job => {
      jobsById[job.id] = job;
    });
    set({
      jobs: Array.isArray(jobs) ? jobs : [],
      jobsById,
      lastUpdated: Date.now(),
    });
  },

  addJob: (job: any) => {
    set((state: any) => {
      const jobs = Array.isArray(state.jobs) ? [...state.jobs] : [];
      const existingIndex = jobs.findIndex(j => j.id === job.id);

      if (existingIndex >= 0) {
        jobs[existingIndex] = job;
      } else {
        jobs.push(job);
      }

      const jobsById = { ...state.jobsById, [job.id]: job };
      const jobUpdateTimestamps = { ...state.jobUpdateTimestamps, [job.id]: Date.now() };

      return { jobs, jobsById, jobUpdateTimestamps };
    });
  },

  updateJob: (jobId: string, updates: any) => {
    set((state: any) => {
      const jobs = Array.isArray(state.jobs) ? [...state.jobs] : [];
      const jobIndex = jobs.findIndex(j => j.id === jobId);

      if (jobIndex >= 0) {
        jobs[jobIndex] = { ...jobs[jobIndex], ...updates };
      }

      const jobsById = state.jobsById[jobId]
        ? { ...state.jobsById, [jobId]: { ...state.jobsById[jobId], ...updates } }
        : state.jobsById;

      const jobUpdateTimestamps = { ...state.jobUpdateTimestamps, [jobId]: Date.now() };

      return {
        jobs,
        jobsById,
        jobUpdateTimestamps,
        selectedJob:
          state.selectedJob?.id === jobId
            ? { ...state.selectedJob, ...updates }
            : state.selectedJob,
        currentJob:
          state.currentJob?.id === jobId ? { ...state.currentJob, ...updates } : state.currentJob,
      };
    });
  },

  setSelectedJob: (job: any | null) => {
    set({
      selectedJob: job,
      selectedJobId: job?.id || null,
      currentJob: job,
    });
  },

  removeJob: (jobId: string) => {
    set((state: any) => {
      const jobs = Array.isArray(state.jobs) ? state.jobs.filter((j: any) => j.id !== jobId) : [];
      const { [jobId]: removed, ...jobsById } = state.jobsById;
      const { [jobId]: removedTimestamp, ...jobUpdateTimestamps } = state.jobUpdateTimestamps;

      return {
        jobs,
        jobsById,
        jobUpdateTimestamps,
        selectedJob: state.selectedJob?.id === jobId ? null : state.selectedJob,
        currentJob: state.currentJob?.id === jobId ? null : state.currentJob,
        selectedJobId: state.selectedJobId === jobId ? null : state.selectedJobId,
      };
    });
  },

  refreshJob: (jobId: string, updatedJob: any) => {
    set((state: any) => {
      const jobs = Array.isArray(state.jobs) ? [...state.jobs] : [];
      const jobIndex = jobs.findIndex(j => j.id === jobId);

      if (jobIndex >= 0) {
        jobs[jobIndex] = updatedJob;
      }

      const jobsById = { ...state.jobsById, [jobId]: updatedJob };
      const jobUpdateTimestamps = { ...state.jobUpdateTimestamps, [jobId]: Date.now() };

      return {
        jobs,
        jobsById,
        jobUpdateTimestamps,
        selectedJob: state.selectedJob?.id === jobId ? updatedJob : state.selectedJob,
        currentJob: state.currentJob?.id === jobId ? updatedJob : state.currentJob,
      };
    });
  },

  markJobAsUpdated: (jobId: string) => {
    set((state: any) => ({
      jobUpdateTimestamps: { ...state.jobUpdateTimestamps, [jobId]: Date.now() },
    }));
  },

  isJobRecentlyUpdated: (jobId: string, thresholdMs = 5000) => {
    const timestamp = get().jobUpdateTimestamps[jobId];
    return timestamp ? Date.now() - timestamp < thresholdMs : false;
  },

  getJobById: (jobId: string) => {
    const state = get();
    return state.jobsById[jobId] || state.jobs.find((j: any) => j.id === jobId);
  },

  updateMultipleJobs: (updates: Array<{ jobId: string; updates: any }>) => {
    set((state: any) => {
      const jobs = Array.isArray(state.jobs) ? [...state.jobs] : [];
      const jobsById = { ...state.jobsById };
      const jobUpdateTimestamps = { ...state.jobUpdateTimestamps };
      let selectedJob = state.selectedJob;
      let currentJob = state.currentJob;

      updates.forEach(({ jobId, updates: jobUpdates }) => {
        const jobIndex = jobs.findIndex(j => j.id === jobId);
        if (jobIndex >= 0) {
          jobs[jobIndex] = { ...jobs[jobIndex], ...jobUpdates };
        }

        if (jobsById[jobId]) {
          jobsById[jobId] = { ...jobsById[jobId], ...jobUpdates };
        }

        jobUpdateTimestamps[jobId] = Date.now();

        if (selectedJob?.id === jobId) {
          selectedJob = { ...selectedJob, ...jobUpdates };
        }

        if (currentJob?.id === jobId) {
          currentJob = { ...currentJob, ...jobUpdates };
        }
      });

      return { jobs, jobsById, jobUpdateTimestamps, selectedJob, currentJob };
    });
  },

  // ========== Cache and Utility Methods ==========
  clearCache: () => {
    set({ cache: {}, lastFetchTime: {} });
  },

  invalidateCache: (key?: string) => {
    set((state: any) => {
      if (key) {
        const { [key]: removed, ...cache } = state.cache;
        const { [key]: removedTime, ...lastFetchTime } = state.lastFetchTime;
        return { cache, lastFetchTime };
      } else {
        return { cache: {}, lastFetchTime: {} };
      }
    });
  },

  shouldRefetch: (key: string) => {
    const lastFetch = get().lastFetchTime[key] || 0;
    return Date.now() - lastFetch > get().cacheTimeout;
  },

  isDataStale: (maxAgeMs = 30000) => {
    const lastUpdated = get().lastUpdated;
    return Date.now() - lastUpdated > maxAgeMs;
  },

  // ========== Store Management ==========
  reset: () => {
    const initialFormData = {
      jobTitle: '',
      jobType: '',
      jobDescription: '',
      department: '',
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
      requirements: '',
      status: 'DRAFT',
      location: '',
      employmentType: 'FULL_TIME',
      workMode: 'HYBRID',
      benefits: [],
    };

    const initialStats = {
      totalCandidates: 0,
      topTierCount: 0,
      secondTierCount: 0,
      othersCount: 0,
      unrankedCount: 0,
      shortlistedCount: 0,
      totalApplications: 0,
      viewCount: 0,
      publishedPlatformsCount: 0,
    };

    set({
      jobs: [],
      jobsById: {},
      candidates: {},
      allCandidatesFlat: [],
      workerJobs: {},
      currentJobDetails: null,
      stats: initialStats,
      selectedJobId: null,
      currentJob: null,
      selectedJob: null,
      isLoading: false,
      isSaving: false,
      isPublishing: false,
      isUnpublishing: false,
      isProcessing: false,
      error: null,
      formData: initialFormData,
      originalFormData: null,
      hasUnsavedChanges: false,
      validationErrors: {},
      currentPage: 1,
      pageSize: 20,
      totalPages: 0,
      filters: {},
      lastUpdated: 0,
      jobUpdateTimestamps: {},
      cache: {},
      lastFetchTime: {},
      isResumeUploadActive: false,
      activeJobs: [],
      optimisticUpdates: {},
      silentUpdateInProgress: false,
      pendingRequests: {},
      availablePlatforms: [],
      selectedPlatforms: [],
      company: null,
      lastCompanyFetch: null,
      isHydrated: false,
      userRole: null,
    });
  },

  resetStore: () => {
    get().reset();
  },
});

// ============================================================================
// UNIFIED JOB STORE - CACHE UTILITIES
// ============================================================================
// Utility functions for cache management to avoid circular dependencies

export const clearUserCaches = () => {
  if (typeof window !== 'undefined') {
    const keys = Object.keys(localStorage);
    const userCacheKeys = keys.filter(key => key.startsWith('user_cache_'));
    userCacheKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    return userCacheKeys.length;
  }
  return 0;
};

export const clearJobsCacheByEndpoint = (endpoint: string) => {
  if (typeof window !== 'undefined') {
    const keys = Object.keys(localStorage);
    const jobsCacheKeys = keys.filter(key => key.includes(`jobs_cache_${endpoint}`));
    jobsCacheKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    return jobsCacheKeys.length;
  }
  return 0;
};
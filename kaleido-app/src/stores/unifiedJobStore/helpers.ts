// ============================================================================
// UNIFIED JOB STORE HELPERS - Utility Functions
// ============================================================================
// Helper functions for job data management and localStorage operations

import { IJob } from '@/entities/interfaces';
import type { JobFormData } from './types';

// ========== Default Data Factories ==========
export const getDefaultJob = (): IJob => ({
  id: '',
  jobTitle: '',
  jobType: '',
  department: '',
  topCandidateThreshold: 0,
  secondTierCandidateThreshold: 0,
  requirements: [],
  status: 'DRAFT',
  location: [],
  benefits: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  clientId: '',
  matchedCandidatesCount: 0,
  candidatesCount: 0,
});

// Export as defaultJob for backward compatibility
export const defaultJob = getDefaultJob();

export const getDefaultFormData = (): JobFormData => ({
  jobTitle: '',
  jobType: '',
  jobDescription: '',
  department: '',
  topCandidateThreshold: 0,
  secondTierCandidateThreshold: 0,
  requirements: '',
  status: 'DRAFT',
  location: '',
  employmentType: 'FULL_TIME',
  workMode: 'HYBRID',
  benefits: [],
});

// ========== LocalStorage Operations ==========
const JOB_STORAGE_KEY = 'currentJobDraft';
const ACTIVE_STEP_KEY = 'activeStep';

export const saveJobToLocalStorage = (job: IJob): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(JOB_STORAGE_KEY, JSON.stringify(job));
  } catch (error) {
    console.warn('Failed to save job to localStorage:', error);
  }
};

export const loadJobFromLocalStorage = (): IJob | null => {
  if (typeof window === 'undefined') return null;

  try {
    const saved = localStorage.getItem(JOB_STORAGE_KEY);
    if (saved) {
      return JSON.parse(saved);
    }
  } catch (error) {
    console.warn('Failed to load job from localStorage:', error);
  }

  return null;
};

export const clearJobFromLocalStorage = (): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem(JOB_STORAGE_KEY);
    localStorage.removeItem(ACTIVE_STEP_KEY);
  } catch (error) {
    console.warn('Failed to clear job from localStorage:', error);
  }
};

export const saveActiveStepToLocalStorage = (step: number): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(ACTIVE_STEP_KEY, step.toString());
  } catch (error) {
    console.warn('Failed to save active step to localStorage:', error);
  }
};

export const loadActiveStepFromLocalStorage = (): number => {
  if (typeof window === 'undefined') return 0;

  try {
    const saved = localStorage.getItem(ACTIVE_STEP_KEY);
    if (saved) {
      return parseInt(saved, 10);
    }
  } catch (error) {
    console.warn('Failed to load active step from localStorage:', error);
  }

  return 0;
};

// ========== Data Transformation Helpers ==========
export const transformJobToFormData = (job: IJob): JobFormData => ({
  jobTitle: job.jobTitle || '',
  jobType: job.jobType || '',
  jobDescription: job.finalDraft || job.generatedJD || '',
  department: job.department || '',
  topCandidateThreshold: job.topCandidateThreshold || 0,
  secondTierCandidateThreshold: job.secondTierCandidateThreshold || 0,
  requirements: Array.isArray(job.requirements) ? job.requirements.join('\n') : '',
  status: (job.status as any) || 'DRAFT',
  location: Array.isArray(job.location) ? job.location.join(', ') : job.location || '',
  employmentType: 'FULL_TIME',
  workMode: 'HYBRID',
  benefits: job.benefits || [],
});

export const transformFormDataToJob = (formData: JobFormData, existingJob?: IJob): IJob => {
  const requirements =
    typeof formData.requirements === 'string'
      ? formData.requirements.split('\n').filter(req => req.trim() !== '')
      : formData.requirements || [];

  return {
    ...getDefaultJob(),
    ...existingJob,
    jobTitle: formData.jobTitle,
    jobType: formData.jobType,
    finalDraft: formData.jobDescription,
    department: formData.department,
    topCandidateThreshold: formData.topCandidateThreshold,
    secondTierCandidateThreshold: formData.secondTierCandidateThreshold,
    requirements,
    status: formData.status as any,
    location: [formData.location],
    benefits: formData.benefits,
    updatedAt: new Date(),
  };
};

// ========== Validation Helpers ==========
export const validateJobFormData = (formData: JobFormData): { [field: string]: string } => {
  const errors: { [field: string]: string } = {};

  if (!formData.jobTitle?.trim()) {
    errors.jobTitle = 'Job title is required';
  }

  if (!formData.jobType?.trim()) {
    errors.jobType = 'Job type is required';
  }

  if (!formData.jobDescription?.trim()) {
    errors.jobDescription = 'Job description is required';
  }

  if (!formData.department?.trim()) {
    errors.department = 'Department is required';
  }

  if (formData.topCandidateThreshold < 0 || formData.topCandidateThreshold > 100) {
    errors.topCandidateThreshold = 'Top candidate threshold must be between 0 and 100';
  }

  if (formData.secondTierCandidateThreshold < 0 || formData.secondTierCandidateThreshold > 100) {
    errors.secondTierCandidateThreshold = 'Second tier threshold must be between 0 and 100';
  }

  if (formData.topCandidateThreshold <= formData.secondTierCandidateThreshold) {
    errors.thresholds = 'Top candidate threshold must be higher than second tier threshold';
  }

  return errors;
};

// ========== Data Processing Helpers ==========
export const groupCandidatesByTier = (candidates: any[], job: IJob) => {
  const topTier: any[] = [];
  const secondTier: any[] = [];
  const others: any[] = [];
  const unranked: any[] = [];
  const shortlisted: any[] = [];

  candidates.forEach(candidate => {
    if (candidate.isShortlisted) {
      shortlisted.push(candidate);
    }

    if (candidate.score >= (job.topCandidateThreshold || 0)) {
      topTier.push(candidate);
    } else if (candidate.score >= (job.secondTierCandidateThreshold || 0)) {
      secondTier.push(candidate);
    } else if (candidate.score > 0) {
      others.push(candidate);
    } else {
      unranked.push(candidate);
    }
  });

  return { topTier, secondTier, others, unranked, shortlisted };
};

export const calculateJobStats = (candidates: any[], job: IJob) => {
  const grouped = groupCandidatesByTier(candidates, job);

  return {
    totalCandidates: candidates.length,
    topTierCount: grouped.topTier.length,
    secondTierCount: grouped.secondTier.length,
    othersCount: grouped.others.length,
    unrankedCount: grouped.unranked.length,
    shortlistedCount: grouped.shortlisted.length,
    totalApplications: candidates.length,
    viewCount: job.applicantCount || 0,
    publishedPlatformsCount: job.status === 'PUBLISHED' ? 1 : 0,
  };
};

// ========== Utility Functions ==========
export const generateJobId = (): string => {
  return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const isJobPublished = (job: IJob): boolean => {
  return job.status === 'PUBLISHED';
};

export const canEditJob = (job: IJob): boolean => {
  return ['DRAFT', 'PAUSED'].includes(job.status || '');
};

export const formatJobTitle = (title: string): string => {
  return title.trim().replace(/\s+/g, ' ');
};

export const slugifyJobTitle = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// ========== Cache Helpers ==========
export const isCacheValid = (timestamp: number, maxAge: number = 300000): boolean => {
  return Date.now() - timestamp < maxAge;
};

export const generateCacheKey = (prefix: string, ...args: (string | number)[]): string => {
  return `${prefix}_${args.join('_')}`;
};

// ========== Error Handling Helpers ==========
export const handleAsyncError = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'An unexpected error occurred';
};

export const isNetworkError = (error: any): boolean => {
  return error?.code === 'NETWORK_ERROR' || error?.message?.includes('network');
};

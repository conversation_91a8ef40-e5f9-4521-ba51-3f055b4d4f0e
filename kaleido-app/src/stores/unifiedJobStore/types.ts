// ============================================================================
// UNIFIED JOB STORE TYPES - Type Definitions
// ============================================================================
// Comprehensive type definitions for the unified job store

import { IJob } from '@/entities/interfaces';

// ========== Core Job Data Types ==========
export interface JobData {
  id: string;
  jobTitle: string;
  jobType: string;
  jobDescription: string;
  department: string;
  topCandidateThreshold: number;
  secondTierCandidateThreshold: number;
  requirements: string[];
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'CLOSED' | 'PUBLISHED';
  location: string;
  employmentType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP';
  workMode: 'REMOTE' | 'ONSITE' | 'HYBRID';
  benefits: string[];
  createdAt: string;
  updatedAt: string;
  clientId: string;
  userId: string;
  isPublished: boolean;
  publishedAt?: string;
  viewCount: number;
  applicationCount: number;

  // Additional fields for backward compatibility
  companyName?: string;
  companyId?: string;
  publishedPlatforms?: string[];
  salaryRange?: string;
  applicationDeadline?: string;
  matchRankCost?: {
    success: boolean;
    creditCost: number;
    unevaluatedCandidatesCount: number;
    isValid: boolean;
    message: string;
    availableCredits: number;
  };
  totalCandidates?: number;
  recentCandidates?: Array<{
    id: string;
    fullName: string;
    originalFilename?: string;
    createdAt: string;
  }>;
  cultureFitQuestions?: string[] | { id: string; question: string; duration: number }[];
  cultureFitDescription?: string;
  candidates?: any[];

  // Required IJob fields for compatibility
  matchedCandidatesCount: any;
  candidatesCount: any;

  // Other optional IJob fields that might be used
  title?: string;
  industry?: string;
  experience?: string;
  experienceLevel?: string;
  currency?: string;
  paymentPeriod?: string;
  skills?: string[];
  responsibilities?: string[];
  jobResponsibilities?: string[];
  hiringManagerDescription?: string;
  companyWebsite?: string;
  companyDescription?: string;
  [key: string]: any; // Allow additional properties
}

// ========== Form Data Types ==========
export interface JobFormData {
  jobTitle: string;
  jobType: string;
  jobDescription: string;
  department: string;
  topCandidateThreshold: number;
  secondTierCandidateThreshold: number;
  requirements: string;
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'CLOSED' | 'PUBLISHED';
  location: string;
  employmentType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP';
  workMode: 'REMOTE' | 'ONSITE' | 'HYBRID';
  benefits: string[];
}

// ========== Candidate Management Types ==========
export interface CandidateData {
  id: string;
  name: string;
  email: string;
  phone?: string;
  resumeUrl?: string;
  score: number;
  tier: 'top' | 'second' | 'other' | 'unranked';
  applicationDate: string;
  status: 'applied' | 'shortlisted' | 'interviewed' | 'rejected' | 'hired';

  // Additional fields for backward compatibility
  fullName?: string;
  jobTitle?: string;
  createdAt?: string;
  updatedAt?: string;
  evaluation?: {
    matchScore?: number;
    rank?: number;
  };
  evaluations?: Array<{
    id: string;
    jobId: string;
    matchScore?: number;
    evaluation?: {
      rank?: number;
      [key: string]: any;
    };
  }>;
}

export interface GroupedCandidates {
  [jobId: string]: {
    topTier: CandidateData[];
    secondTier: CandidateData[];
    others: CandidateData[];
    unranked: CandidateData[];
    shortlisted: CandidateData[];
  };
}

// ========== Statistics Types ==========
export interface JobStoreStats {
  totalCandidates: number;
  topTierCount: number;
  secondTierCount: number;
  othersCount: number;
  unrankedCount: number;
  shortlistedCount: number;
  totalApplications: number;
  viewCount: number;
  publishedPlatformsCount: number;
}

// ========== Worker Job Types ==========
export interface WorkerJob {
  id: string;
  jobId: string;
  relatedId: string;
  type: 'upload' | 'scout' | 'matchrank' | 'ats';
  status:
    | 'queued'
    | 'active'
    | 'completed'
    | 'failed'
    | 'cancelled'
    | 'completed_with_errors'
    | 'processing';
  progress: number;
  totalFiles: number;
  processedFiles: number;
  errorCount: number;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
  data?: any;
  error?: string;
  result?: any;
  message?: string;
  failedCount?: number;
  metadata?: any;
}

// ========== Publishing Platform Types ==========
export interface PublishPlatform {
  id: string;
  name: string;
  enabled: boolean;
  config?: any;
}

// ========== Company Data Types ==========
export interface CompanyData {
  companyName: string;
  companyWebsite: string;
  contactEmail: string;
  phoneNumber: string;
  clientId: string;
  id: string;
  name: string;
  logo?: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  location?: string;
}

// ========== Cache and State Types ==========
export interface CacheData {
  [key: string]: any;
}

export interface LastFetchTime {
  [key: string]: number;
}

export interface JobUpdateTimestamps {
  [jobId: string]: number;
}

export interface ValidationErrors {
  [field: string]: string;
}

export interface OptimisticUpdates {
  [jobId: string]: any;
}

export interface PendingRequests {
  [key: string]: Promise<any>;
}

// ========== Job Details Types ==========
export interface JobDetailsData extends Partial<IJob> {
  id: string;
  [key: string]: any; // Placeholder for additional properties
}

// ========== Additional Missing Types ==========
export interface MinimalCandidate {
  id: string;
  name: string;
  email: string;
  score: number;
  tier: string;
  fullName?: string;
  jobTitle?: string;
  status?: string;
  contacted?: boolean;
  isShortlisted?: boolean;
  originalFilename?: string;
  createdAt?: string;
  updatedAt?: string;
  evaluations?: Array<{
    id: string;
    jobId: string;
    matchScore?: number;
    evaluation?: {
      rank?: number;
      [key: string]: any;
    };
  }>;
  evaluation?: {
    matchScore?: number;
    rank?: number;
  };
}

export interface UploadJob {
  jobId: string;
  status: 'queued' | 'active' | 'completed' | 'failed' | 'cancelled' | 'completed_with_errors';
  progress: number;
  totalFiles: number;
  processedFiles: number;
  errorCount?: number;
  createdAt: string;
  updatedAt: string;
  data?: any;
  error?: string;
}

// ========== Main Store State Interface ==========
export interface UnifiedJobStoreState {
  // ========== Core Job Data ==========
  jobs: JobData[];
  jobsById: { [jobId: string]: JobData };
  candidates: GroupedCandidates;
  allCandidatesFlat: CandidateData[];
  workerJobs: { [jobId: string]: WorkerJob };

  // ========== Job Selection ==========
  currentJobDetails: JobDetailsData | null;
  selectedJobId: string | null;
  currentJob: JobData | null;
  selectedJob: JobData | null;

  // ========== Loading States ==========
  isLoading: boolean;
  isSaving: boolean;
  isPublishing: boolean;
  isUnpublishing: boolean;
  isProcessing: boolean;
  error: string | null;

  // ========== Form Management ==========
  formData: JobFormData;
  originalFormData: JobFormData | null;
  hasUnsavedChanges: boolean;
  validationErrors: ValidationErrors;

  // ========== Pagination ==========
  currentPage: number;
  pageSize: number;
  totalPages: number;
  filters: any;

  // ========== Statistics ==========
  stats: JobStoreStats;

  // ========== Match Rank Cost ==========
  matchRankCost: {
    success: boolean;
    creditCost: number;
    unevaluatedCandidatesCount: number;
    isValid: boolean;
    message: string;
    availableCredits: number;
  } | null;

  // ========== Cache Management ==========
  lastUpdated: number;
  jobUpdateTimestamps: JobUpdateTimestamps;
  cache: CacheData;
  lastFetchTime: LastFetchTime;
  cacheTimeout: number;

  // ========== Optimistic Updates ==========
  optimisticUpdates: OptimisticUpdates;
  silentUpdateInProgress: boolean;
  pendingRequests: PendingRequests;
  lastJobsByStatusFetch: { [key: string]: number };

  // ========== Upload/Worker Management ==========
  isResumeUploadActive: boolean;
  activeJobs: string[];

  // ========== Publishing ==========
  availablePlatforms: PublishPlatform[];
  selectedPlatforms: string[];

  // ========== Job Wizard State ==========
  company: CompanyData | null;
  lastCompanyFetch: number | null;
  isHydrated: boolean;
  userRole: string | null;
  job: IJob;
  activeStep: number;
  totalSteps: number;
  isFinalStep: boolean;
  isPreview: boolean;
  fadeIn: boolean;
  steps: any[];

  // ========== Jobs By Status ==========
  jobsByStatus: { [key: string]: { jobs: JobData[]; count: number; stats?: any } };
  jobsByStatusPagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

import { getSession, getAccessToken } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function me(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get the session using Auth0's getSession
    const session = await getSession(req, res);

    // Check if we have a valid session with user data
    if (!session?.user) {
      return res.status(401).json({
        user: null,
        error: 'Not authenticated',
        code: 'NO_SESSION',
        requiresLogout: true,
      });
    }

    // Get access token with proper error handling
    let accessToken = null;
    let tokenError = null;

    try {
      const tokenResult = await getAccessToken(req, res, {
        refresh: true, // Force refresh if needed
      });

      if (tokenResult && typeof tokenResult === 'string') {
        accessToken = tokenResult;
      } else if (tokenResult && typeof tokenResult === 'object' && 'accessToken' in tokenResult) {
        accessToken = tokenResult.accessToken;
      }
    } catch (error: any) {
      tokenError = error;
      console.error('Error getting access token:', error);

      // Check if this is a refresh token error - requires full logout
      if (
        error.code === 'ERR_EXPIRED_ACCESS_TOKEN' ||
        error.message?.includes('refresh token is not available') ||
        error.message?.includes('expired')
      ) {
        // Clear the Auth0 session cookie to force a clean state
        res.setHeader('Set-Cookie', [
          'appSession=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
          'appSession.0=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
          'appSession.1=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
          'appSession.2=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; HttpOnly; SameSite=Lax',
        ]);

        return res.status(401).json({
          user: null,
          error: 'Session expired. Please log in again.',
          code: 'SESSION_EXPIRED',
          requiresLogout: true,
        });
      }
    }

    // Return session data even if we couldn't get a fresh access token
    // This allows the app to function with a valid session but expired token
    const responseData = {
      ...session.user, // Include all user fields
      user: session.user,
      accessToken: accessToken,
      scope: session.accessTokenScope,
      expiresAt: session.accessTokenExpiresAt,
      idToken: session.idToken,
      tokenError: tokenError ? tokenError.message : null,
    };

    // Set cache control headers
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.json(responseData);
  } catch (error: any) {
    console.error('Error fetching user data:', error);

    // Provide more specific error codes
    const errorCode = error.code || 'UNKNOWN_ERROR';
    const requiresLogout =
      errorCode === 'ERR_EXPIRED_ACCESS_TOKEN' ||
      error.message?.includes('expired') ||
      error.message?.includes('refresh');

    res.status(401).json({
      user: null,
      error: error instanceof Error ? error.message : 'Failed to fetch user data',
      code: errorCode,
      requiresLogout,
    });
  }
}

import React, { useCallback, useEffect, useRef, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Building2, Calendar, CheckCircle, LayoutGrid, Plus, Table, Users } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import Ranked from '@/components/Ranked';
import { IJob } from '@/entities/interfaces';
import { useCurrentUser } from '@/services/user.service';
import { useJobsStore, jobDataArrayToIJobArray } from '@/stores/unifiedJobStore';

import CultureFitDetailsSettings from '../CultureFit/CultureFitDetailsSettings';
import PaginationDetailsLayout from '../Layouts/PaginationDetailsLayout';
import CompactFilter from '../ui/CompactFilter';
import { EmptyState } from '../ui/EmptyState';
import VideoJDDetailsSettings from '../VideoJD/VideoJDDetailsSettings';
import { PostedJobDetails } from './PostedJobDetails';
import PostedJobList from './PostedJobList';

// Status display names and their order
const JOB_STATUSES = [
  {
    id: 'ALL',
    label: 'All',
    icon: <LayoutGrid className="w-4 h-4" />,
    tooltip: 'Show all jobs regardless of status',
  },
  {
    id: 'NEW',
    label: 'New',
    icon: <CheckCircle className="w-4 h-4" />,
    tooltip: "Jobs that are newly created or don't have evaluated candidates yet",
  },
  {
    id: 'MATCHED',
    label: 'Ranked',
    icon: <Users className="w-4 h-4" />,
    tooltip: 'Jobs with candidates that have been evaluated and ranked by match score',
  },
  {
    id: 'INTERVIEWING',
    label: 'Interviewing',
    icon: <Calendar className="w-4 h-4" />,
    tooltip: 'Jobs with candidates in the interview process',
  },
  {
    id: 'HIRED',
    label: 'Hired',
    icon: <Building2 className="w-4 h-4" />,
    tooltip: 'Jobs with candidates who have been hired',
  },
];

// View types
const VIEW_TYPES = {
  TABLE: 'table',
  RANKED: 'ranked',
};

interface JobsByStatusProps {
  onJobClick?: (job: IJob) => void;
  initialView?: string | null;
  isClientLoading?: boolean;
}

const JobsByStatusInner: React.FC<JobsByStatusProps> = ({
  onJobClick,
  initialView,
  isClientLoading = false,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [viewType, setViewType] = useState(
    initialView === 'ranked' ? VIEW_TYPES.RANKED : VIEW_TYPES.TABLE
  );

  const user = useCurrentUser();
  const fetchInProgress = useRef(false);

  // Use jobsStore for all job-related state and operations
  const jobsByStatus = useJobsStore(state => state.jobsByStatus);
  const jobsByStatusPagination = useJobsStore(state => state.jobsByStatusPagination);
  const isLoading = useJobsStore(state => state.isLoading);
  const fetchJobsByStatus = useJobsStore(state => state.fetchJobsByStatus);
  const setSelectedJobId = useJobsStore(state => state.setSelectedJobId);
  const selectedJob = useJobsStore(state => state.selectedJob);
  const selectedJobId = useJobsStore(state => state.selectedJobId);

  // Get current page and status from URL
  const currentPage = parseInt(searchParams?.get('page') || '1', 10);
  const currentStatus = searchParams?.get('status')?.toUpperCase() || 'ALL';
  const jobId = searchParams?.get('jobId');
  const showVideoJd = searchParams?.get('showVideoJd') === 'true';
  const showCultureFit = searchParams?.get('showCultureFit') === 'true';

  // Effect to fetch jobs when URL parameters change - optimized to reduce API calls
  useEffect(() => {
    if (!user) return;

    const hasDataForCurrentStatus =
      jobsByStatus &&
      jobsByStatus[currentStatus] &&
      jobsByStatus[currentStatus].jobs !== undefined &&
      jobsByStatusPagination.currentPage === currentPage;

    // If we already have data for this status and page, don't fetch again
    if (hasDataForCurrentStatus) {
      return;
    }

    // If there's a jobId parameter and we have some jobs data, don't fetch the job list
    // But if we have no jobs data at all (e.g., on page refresh), we should still fetch
    const hasAnyJobsData =
      jobsByStatus &&
      Object.keys(jobsByStatus).some(
        status =>
          jobsByStatus[status] && jobsByStatus[status].jobs && jobsByStatus[status].jobs.length > 0
      );

    if (jobId && hasAnyJobsData) {
      return;
    }

    // Track fetch attempts to prevent infinite loops
    const fetchAttemptKey = `fetch_attempt_${currentStatus}_${currentPage}`;
    const fetchAttempts = parseInt(sessionStorage.getItem(fetchAttemptKey) || '0', 10);

    // If we've tried too many times, don't keep trying
    if (fetchAttempts > 3) {
      return;
    }

    const fetchJobs = async () => {
      if (fetchInProgress.current) {
        return;
      }

      try {
        fetchInProgress.current = true;
        // Increment fetch attempt counter
        sessionStorage.setItem(fetchAttemptKey, (fetchAttempts + 1).toString());
        await fetchJobsByStatus(currentPage, currentStatus);
        // Reset counter on success
        sessionStorage.removeItem(fetchAttemptKey);
      } catch (error) {
        console.error('Error fetching jobs:', error);
      } finally {
        fetchInProgress.current = false;
      }
    };
    fetchJobs();
  }, [
    user,
    currentPage,
    currentStatus,
    searchParams,
    fetchJobsByStatus,
    jobsByStatus,
    jobsByStatusPagination,
    jobId,
  ]);

  // Effect to handle jobId parameter
  useEffect(() => {
    // If showVideoJd=true or showCultureFit=true, skip setting selectedJobId to prevent automatic job fetching
    if (jobId) {
      // Only set if it's different from current selectedJobId to avoid unnecessary fetches
      if (selectedJobId !== jobId) {
        setSelectedJobId(jobId);
      }
    } else {
      setSelectedJobId(null);
    }
  }, [jobId, selectedJobId, setSelectedJobId, selectedJob]);

  // Handle page change
  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage < 1) return;

      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('page', newPage.toString());
      router.push(`?${newParams.toString()}`);
    },
    [router, searchParams]
  );

  // Handle status change
  const handleStatusChange = useCallback(
    (newStatus: string) => {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set('status', newStatus);
      newParams.set('page', '1'); // Reset to first page when changing status
      router.push(`?${newParams.toString()}`);
    },
    [router, searchParams]
  );

  // Handle job click
  const handleJobClick = useCallback(
    (job: IJob) => {
      setSelectedJobId(job.id);
      if (onJobClick) {
        onJobClick(job);
      }
    },
    [onJobClick, setSelectedJobId]
  );

  // Navigate to job description creation page
  const handleCreateNewJD = useCallback(() => {
    // Clear any existing job data and editing flags
    localStorage.removeItem('job');
    localStorage.removeItem('step');
    localStorage.removeItem('activeStep');
    localStorage.removeItem('isEditingJob');
    localStorage.removeItem('editingJobId');
    // Clear the edit refresh flag
    sessionStorage.removeItem('loadedEditJobId');

    // Also clear the Zustand store
    const { clearJobData } = useJobsStore.getState();
    clearJobData();

    // Navigate to job creation page
    // The JobsContext will handle showing the draft modal if there's a valid draft
    router.push('/job-description-creation');
  }, [router]);

  if (!user) {
    return null;
  }

  if (showVideoJd) {
    return <VideoJDDetailsSettings onCloseOverride={() => {}} job={undefined} />;
  }

  if (showCultureFit) {
    return (
      <CultureFitDetailsSettings
        onCloseOverride={() => {
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.delete('showCultureFit');
          newParams.delete('jobId');
          const newUrl = newParams.toString() ? `?${newParams.toString()}` : '/jobs';
          router.push(newUrl);
        }}
      />
    );
  }

  // Loading state is now passed to PostedJobList component

  // If no job status data, show empty state
  if (!jobsByStatus || Object.keys(jobsByStatus).length === 0) {
    return (
      <EmptyState
        title="No Jobs Available"
        description="There are no jobs available at this time. Create your first job description to get started."
        actionLabel="Create New Job Description"
        actionRoute="/job-description-creation"
        onAction={handleCreateNewJD}
      />
    );
  }

  // Get the current status from URL or use default
  const effectiveActiveStatus = currentStatus || 'ALL';

  // Get jobs to display for current active status
  const jobsToDisplay = jobsByStatus[effectiveActiveStatus]?.jobs || [];

  // Get pagination info
  const paginationInfo = {
    currentPage: jobsByStatusPagination.currentPage,
    totalPages: jobsByStatusPagination.totalPages,
    totalItems: jobsByStatus[effectiveActiveStatus]?.count || 0,
    itemsPerPage: jobsByStatusPagination.itemsPerPage,
  };

  // Check if there are any jobs to display
  const hasJobs = jobsToDisplay.length > 0;

  // If there's a jobId parameter, we should show the job details instead of the job list
  if (jobId) {
    return (
      <div className="relative">
        {selectedJob && selectedJobId === jobId ? (
          <>
            <PostedJobDetails
              job={selectedJob}
              onClose={() => {
                setSelectedJobId(null);
                const newParams = new URLSearchParams(searchParams.toString());
                newParams.delete('jobId');
                newParams.delete('candidateId');
                newParams.delete('showVideoJd');
                newParams.delete('showCultureFit');
                router.push(`?${newParams.toString()}`);
              }}
            />
          </>
        ) : (
          // Show loading state while job is being fetched
          <div className="flex items-center justify-center h-64">
            <div className="text-white/60">Loading job details...</div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Main action bar - prioritize Create New JD button */}
      <div className="flex items-center justify-between mb-4 px-0 md:px-1">
        {/* Left side - compact filter */}
        <div className="flex items-center gap-3">
          <CompactFilter
            options={JOB_STATUSES.map(status => ({
              value: status.id,
              label: status.label,
              icon: status.icon,
              description: status.tooltip,
              count: jobsByStatus[status.id]?.count,
            }))}
            value={effectiveActiveStatus}
            onChange={handleStatusChange}
            placeholder="All Jobs"
            showCounts={true}
            className="min-w-fit"
          />

          {effectiveActiveStatus === 'MATCHED' && (
            <div className="flex items-center space-x-1 bg-white/5 backdrop-blur-sm rounded-xl p-1 shadow-sm border border-white/10">
              <button
                type="button"
                onClick={() => setViewType(VIEW_TYPES.TABLE)}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewType === VIEW_TYPES.TABLE
                    ? 'bg-white/25 text-white shadow-sm border border-white/20'
                    : 'text-white/70 hover:text-white/90 hover:bg-white/10'
                }`}
                aria-label="Table view"
                title="Table view"
              >
                <Table className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => setViewType(VIEW_TYPES.RANKED)}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewType === VIEW_TYPES.RANKED
                    ? 'bg-white/25 text-white shadow-sm border border-white/20'
                    : 'text-white/70 hover:text-white/90 hover:bg-white/10'
                }`}
                aria-label="Grid view"
                title="Ranked view"
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Right side - primary action */}
        <button
          type="button"
          onClick={handleCreateNewJD}
          className="flex items-center space-x-2 px-6 py-2.5 text-sm backdrop-blur-sm bg-white/10 hover:bg-white/20 text-white rounded-xl shadow-sm transition-all duration-200 border border-white/20 hover:border-white/30 focus:outline-none focus:ring-2 focus:ring-white/20 focus:ring-offset-2 focus:ring-offset-transparent"
          aria-label="Create New JD"
          title="Create New Job Description"
        >
          <Plus className="w-4 h-4 text-white" />
          <span className="font-medium">Create New JD</span>
        </button>
      </div>

      {/* Content section - conditionally show EmptyState or job data */}
      {!hasJobs ? (
        <AnimatePresence mode="wait">
          <motion.div
            key="empty-state"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.4,
              ease: [0.22, 1, 0.36, 1],
            }}
            className="w-full"
          >
            <EmptyState
              title={
                effectiveActiveStatus === 'ALL'
                  ? 'No Jobs Found'
                  : `No ${JOB_STATUSES.filter(s => s.id === effectiveActiveStatus)[0]?.label || ''} Jobs Found`
              }
              description={
                effectiveActiveStatus === 'ALL'
                  ? "You don't have any jobs yet. Create your first job description to get started."
                  : `You don't have any jobs in the ${JOB_STATUSES.filter(s => s.id === effectiveActiveStatus)[0]?.label || ''} category yet.`
              }
              actionLabel="Create New Job Description"
              actionRoute="/job-description-creation"
              onAction={handleCreateNewJD}
            />
          </motion.div>
        </AnimatePresence>
      ) : (
        <AnimatePresence mode="wait">
          {effectiveActiveStatus === 'MATCHED' && viewType === VIEW_TYPES.RANKED ? (
            <motion.div
              key="ranked-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: [0.22, 1, 0.36, 1],
              }}
              className="w-full"
            >
              <Ranked />
            </motion.div>
          ) : (
            <motion.div
              key="table-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: [0.22, 1, 0.36, 1],
              }}
              className="w-full"
            >
              <PaginationDetailsLayout
                items={jobsToDisplay.map(job => ({ ...job, id: job.id || '' }))}
                itemsPerPage={paginationInfo.itemsPerPage}
                currentPage={jobsByStatusPagination.currentPage}
                totalPages={jobsByStatusPagination.totalPages}
                isLoading={isLoading}
                idParam="jobId"
                onPageChange={handlePageChange}
                detailsView={() => {
                  // If we have a jobId but no selectedJob yet, and we're loading, show the modal with loading state
                  if (jobId && !selectedJob && isLoading) {
                    return (
                      <PostedJobDetails
                        job={null}
                        onClose={() => {
                          setSelectedJobId(null);
                          const newParams = new URLSearchParams(searchParams.toString());
                          newParams.delete('jobId');
                          newParams.delete('candidateId');
                          router.push(`?${newParams.toString()}`);
                        }}
                      />
                    );
                  }

                  if (!selectedJob) return null;

                  return (
                    <PostedJobDetails
                      job={selectedJob}
                      onClose={() => {
                        setSelectedJobId(null);
                        const newParams = new URLSearchParams(searchParams.toString());
                        newParams.delete('jobId');
                        newParams.delete('candidateId');
                        router.push(`?${newParams.toString()}`);
                      }}
                    />
                  );
                }}
                renderContent={paginatedJobs => (
                  <PostedJobList
                    jobs={jobDataArrayToIJobArray(paginatedJobs)}
                    onJobClick={handleJobClick}
                    paginationData={paginationInfo}
                    onPageChange={handlePageChange}
                    isLoading={isLoading || isClientLoading}
                  />
                )}
              />
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

// Export the inner component directly without additional Suspense
const JobsByStatus: React.FC<JobsByStatusProps> = ({ onJobClick, initialView }) => {
  // We'll use useEffect with an empty dependency array to ensure this only runs once on client
  const [isClient, setIsClient] = React.useState(false);

  React.useEffect(() => {
    setIsClient(true);
  }, []);

  // If we're not on the client yet, pass isLoading=true to the inner component
  // This will show the skeleton loader instead of ColorfulSmokeyOrbLoader
  if (!isClient) {
    return (
      <JobsByStatusInner onJobClick={onJobClick} initialView={initialView} isClientLoading={true} />
    );
  }

  return <JobsByStatusInner onJobClick={onJobClick} initialView={initialView} />;
};

export default JobsByStatus;

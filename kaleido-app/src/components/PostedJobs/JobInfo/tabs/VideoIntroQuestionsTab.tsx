import CulturalFitCard from '@/components/CultureFit/CulturalFitCard';
import QuestionSuggestions from '@/components/CultureFit/QuestionSuggestions';
import { showToast } from '@/components/Toaster';
import { IJob } from '@/entities/interfaces';
import { CultureFitQuestions } from '@/entities/Job.entities';
import apiHelper from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useJobStateStore, jobDataToIJob } from '@/stores/unifiedJobStore';
import { Slider } from '@mui/material';
import { motion } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  Loader2,
  MessageSquare,
  Plus,
  Save,
  Sparkles,
  Target,
  Timer,
  Trash2,
  UserCheck,
  Users2,
  Video,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface VideoIntroQuestionsTabProps {
  job: IJob;
}

export const VideoIntroQuestionsTab: React.FC<VideoIntroQuestionsTabProps> = ({
  job: initialJob,
}) => {
  const [job, setJob] = useState<IJob>(initialJob);
  const [questions, setQuestions] = useState<CultureFitQuestions[]>(
    initialJob.cultureFitQuestions?.length
      ? initialJob.cultureFitQuestions
      : [{ id: '1', question: '', duration: 1 }]
  );
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialQuestions, setInitialQuestions] = useState<CultureFitQuestions[]>([]);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const candidatesPerPage = 5;

  // Subscribe to job state changes
  useEffect(() => {
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        const updatedJob = jobs.find(j => j.id === job.id);
        if (updatedJob) {
          const currentJobKey = JSON.stringify({
            cultureFitQuestions: job.cultureFitQuestions,
            candidates: job.candidates?.length,
          });

          const updatedJobKey = JSON.stringify({
            cultureFitQuestions: updatedJob.cultureFitQuestions,
            candidates: updatedJob.candidates?.length,
          });

          if (currentJobKey !== updatedJobKey) {
            setJob(jobDataToIJob(updatedJob));
            setQuestions(
              updatedJob.cultureFitQuestions?.length
                ? Array.isArray(updatedJob.cultureFitQuestions)
                  ? (updatedJob.cultureFitQuestions.filter(
                      q => typeof q === 'object' && 'id' in q
                    ) as CultureFitQuestions[])
                  : [{ id: '1', question: '', duration: 1 }]
                : [{ id: '1', question: '', duration: 1 }]
            );
          }
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, [job.id]);

  // Initialize and fetch culture fit details
  useEffect(() => {
    const initializeJob = async () => {
      if (job.id) {
        setIsLoadingDetails(true);

        try {
          const cultureFitData = await apiHelper.get(`/jobs/${job.id}/culture-fit-details`);

          if (cultureFitData) {
            const updatedJob = {
              ...job,
              ...cultureFitData.job,
              candidates: cultureFitData.candidates,
            };

            setJob(updatedJob);

            if (cultureFitData.job.cultureFitQuestions?.length) {
              setQuestions(cultureFitData.job.cultureFitQuestions);
              setInitialQuestions(cultureFitData.job.cultureFitQuestions);
            }
          }
        } catch (error) {
          console.error('Error fetching culture fit details:', error);
        } finally {
          setIsLoadingDetails(false);
        }
      }
    };

    initializeJob();
  }, [job.id]);

  // Track changes
  useEffect(() => {
    const hasChanges = JSON.stringify(questions) !== JSON.stringify(initialQuestions);
    setHasUnsavedChanges(hasChanges);
  }, [questions, initialQuestions]);

  // Validation
  const validationErrors = React.useMemo(() => {
    const errors: Record<string, string> = {};
    const validQuestions = questions.filter(q => q.question.trim() !== '');
    const hasEmptyQuestions = questions.some(q => q.question.trim() === '');

    if (validQuestions.length === 0) {
      errors.questions = 'At least one question must have content';
    }

    if (hasEmptyQuestions && questions.length > 1) {
      errors.emptyQuestions = 'Please fill in all questions or remove empty ones';
    }

    return errors;
  }, [questions]);

  const handleSave = async () => {
    setIsSaving(true);

    try {
      const validQuestions = questions.filter(q => q.question.trim() !== '');

      const { updateJobCultureFit } = useJobsStore.getState();
      const response = await updateJobCultureFit(job.id, {
        cultureFitQuestions: validQuestions,
        cultureFitDescription: job.cultureFitDescription || '',
      });

      if (response && response.success) {
        // Get the updated job data with new culture fit questions
        const updatedJobData = response.job || response;

        // Update local state with the new questions
        const newQuestions = updatedJobData.cultureFitQuestions?.length
          ? updatedJobData.cultureFitQuestions
          : [{ id: '1', question: '', duration: 1 }];

        setQuestions(newQuestions);
        setInitialQuestions(newQuestions);
        setHasUnsavedChanges(false);

        // Update the job with the new culture fit questions
        setJob(prevJob => ({
          ...prevJob,
          cultureFitQuestions: newQuestions,
          cultureFitDescription:
            updatedJobData.cultureFitDescription || prevJob.cultureFitDescription,
        }));

        // Mark job as updated and trigger refresh event
        useJobStateStore.getState().markJobAsUpdated(job.id);

        // Dispatch event to notify VideoIntroTab
        window.dispatchEvent(
          new CustomEvent('cultureFitUpdated', {
            detail: {
              jobId: job.id,
              cultureFitQuestions: newQuestions,
            },
          })
        );

        showToast({
          message: 'Video intro questions updated successfully',
          isSuccess: true,
        });
      } else {
        showToast({
          message: response?.message || 'Failed to update questions',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error saving questions:', error);
      showToast({
        message: 'Error updating questions',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleQuestionChange = (
    index: number,
    field: keyof CultureFitQuestions,
    value: string | number
  ) => {
    const updatedQuestions = questions.map((q, i) => (i === index ? { ...q, [field]: value } : q));
    setQuestions(updatedQuestions);
  };

  if (isLoadingDetails) {
    return (
      <div className="h-full flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Hero Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden mb-8 -mt-8 -mx-8"
      >
        {/* Background with image and overlay */}
        <div className="relative h-[280px] overflow-hidden">
          {/* Background Image */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url('/images/insights/professional_training_revised.png')`,
              backgroundPosition: 'center 20%',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Gradient overlay - transparent at top, darker at bottom */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-indigo-900/50 to-indigo-900/80" />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent" />

          {/* Content */}
          <div className="relative h-full flex items-center px-8 lg:px-12">
            <div className="max-w-7xl mx-auto w-full">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <div className="flex justify-center mb-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
                    className="p-3 bg-white/20 backdrop-blur-md rounded-2xl border border-white/30"
                  >
                    <Video className="w-10 h-10 text-white" />
                  </motion.div>
                </div>

                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-3xl lg:text-4xl font-bold text-white mb-3"
                >
                  Video Intro Questions
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-lg text-white/90 max-w-2xl mx-auto mb-6"
                >
                  Craft personalized video questions to find candidates aligned with your values
                </motion.p>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex justify-center gap-6"
                >
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5 mb-1">
                      <MessageSquare className="w-4 h-4 text-white/70" />
                      <span className="text-2xl font-bold text-white">
                        {questions.filter(q => q.question.trim()).length}
                      </span>
                    </div>
                    <p className="text-white/60 text-xs">Active Questions</p>
                  </div>

                  <div className="w-px bg-white/20" />

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5 mb-1">
                      <Users2 className="w-4 h-4 text-white/70" />
                      <span className="text-2xl font-bold text-white">
                        {job.candidates?.length || 0}
                      </span>
                    </div>
                    <p className="text-white/60 text-xs">Candidate Responses</p>
                  </div>

                  <div className="w-px bg-white/20" />

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5 mb-1">
                      <Timer className="w-4 h-4 text-white/70" />
                      <span className="text-2xl font-bold text-white">
                        {Math.round(questions.reduce((sum, q) => sum + q.duration, 0) * 10) / 10}
                      </span>
                    </div>
                    <p className="text-white/60 text-xs">Total Minutes</p>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-8 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Questions Section */}
          <div className="lg:col-span-2 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/[0.05]"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-indigo-500/20 rounded-xl">
                  <Sparkles className="w-6 h-6 text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-white">Craft Your Questions</h3>
                  <p className="text-gray-300 text-sm mt-1">
                    Create engaging questions to understand candidates better
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {questions.map((q, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                    className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-5 border border-white/[0.05] hover:border-white/[0.08] transition-all duration-300 relative group"
                  >
                    <div className="flex justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                          {index + 1}
                        </div>
                        <h4 className="text-lg font-medium text-white">Question {index + 1}</h4>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          const newQuestions = questions.filter((_, i) => i !== index);
                          setQuestions(newQuestions);
                        }}
                        disabled={questions.length === 1}
                        className={`p-2 rounded-lg transition-all ${
                          questions.length === 1
                            ? 'text-gray-500 cursor-not-allowed opacity-50'
                            : 'text-red-400 hover:bg-red-500/20 hover:text-red-300'
                        }`}
                      >
                        <Trash2 className="w-4 h-4" />
                      </motion.button>
                    </div>

                    <textarea
                      value={q.question}
                      onChange={e => handleQuestionChange(index, 'question', e.target.value)}
                      placeholder={`Enter an engaging question for candidates to answer...`}
                      className="w-full min-h-[100px] p-4 rounded-xl bg-gray-900/50 border border-gray-700/50 text-gray-200 resize-y focus:outline-none focus:border-indigo-500/50 transition-all duration-300 placeholder:text-gray-500"
                    />

                    {!q.question.trim() && (
                      <div className="mt-4">
                        <QuestionSuggestions
                          onSelectQuestion={question =>
                            handleQuestionChange(index, 'question', question)
                          }
                        />
                      </div>
                    )}

                    <div className="mt-6 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-indigo-400" />
                          <span className="text-sm text-gray-300">Response Duration</span>
                        </div>
                        <span className="text-sm font-medium text-white bg-indigo-500/20 px-3 py-1 rounded-lg">
                          {q.duration} {q.duration === 1 ? 'minute' : 'minutes'}
                        </span>
                      </div>

                      <Slider
                        value={q.duration}
                        onChange={(_, value) =>
                          handleQuestionChange(index, 'duration', value as number)
                        }
                        step={0.5}
                        marks
                        min={0.5}
                        max={3}
                        valueLabelDisplay="auto"
                        sx={{
                          color: '#6366f1',
                          height: 8,
                          '& .MuiSlider-track': {
                            border: 'none',
                            height: 8,
                            borderRadius: 4,
                            background: 'linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%)',
                          },
                          '& .MuiSlider-thumb': {
                            height: 20,
                            width: 20,
                            backgroundColor: '#fff',
                            border: '2px solid #6366f1',
                            '&:hover': {
                              boxShadow: '0 0 0 8px rgba(99, 102, 241, 0.16)',
                            },
                          },
                          '& .MuiSlider-rail': {
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          },
                          '& .MuiSlider-mark': {
                            backgroundColor: 'rgba(255, 255, 255, 0.3)',
                            height: 12,
                            width: 2,
                            borderRadius: 1,
                          },
                          '& .MuiSlider-markActive': {
                            backgroundColor: '#fff',
                          },
                        }}
                      />
                    </div>
                  </motion.div>
                ))}

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() =>
                    setQuestions([
                      ...questions,
                      {
                        id: `${questions.length + 1}`,
                        question: '',
                        duration: 1,
                      },
                    ])
                  }
                  disabled={questions.some(q => q.question.trim() === '')}
                  className={`w-full py-4 rounded-xl flex items-center justify-center gap-3 font-medium transition-all ${
                    questions.some(q => q.question.trim() === '')
                      ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-white hover:from-indigo-500/30 hover:to-purple-500/30 border border-indigo-500/30'
                  }`}
                >
                  <Plus size={20} />
                  Add Another Question
                </motion.button>

                {/* Save Button */}
                <div className="flex justify-center pt-6">
                  <motion.button
                    whileHover={
                      hasUnsavedChanges && Object.keys(validationErrors).length === 0
                        ? { scale: 1.05 }
                        : {}
                    }
                    whileTap={
                      hasUnsavedChanges && Object.keys(validationErrors).length === 0
                        ? { scale: 0.95 }
                        : {}
                    }
                    onClick={handleSave}
                    disabled={
                      isSaving || !hasUnsavedChanges || Object.keys(validationErrors).length > 0
                    }
                    className={`px-10 py-4 rounded-xl flex items-center gap-3 font-semibold text-lg transition-all ${
                      hasUnsavedChanges && Object.keys(validationErrors).length === 0
                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                        : 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Saving Questions...
                      </>
                    ) : (
                      <>
                        <Save className="w-5 h-5" />
                        Save Changes
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Tips Section - Smaller */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-white/[0.01] to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/[0.03]"
            >
              <div className="flex items-center gap-6 text-xs text-gray-500">
                <div className="flex items-center gap-2">
                  <Target className="w-3.5 h-3.5 text-blue-400/60" />
                  <span>
                    <strong className="text-gray-400">Culture Fit</strong>: Questions that reveal
                    values alignment
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <UserCheck className="w-3.5 h-3.5 text-purple-400/60" />
                  <span>
                    <strong className="text-gray-400">Personal Touch</strong>: Open-ended for
                    authentic responses
                  </span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Candidates Section */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/[0.05] h-[calc(100vh-320px)] flex flex-col"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-purple-500/20 rounded-xl">
                  <Users2 className="w-6 h-6 text-purple-400" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white">Candidate Responses</h3>
                  <p className="text-sm text-gray-300 mt-1">
                    {job.candidates?.length || 0}{' '}
                    {job.candidates?.length === 1 ? 'candidate' : 'candidates'} responded
                  </p>
                </div>
              </div>

              {/* Pagination Controls at Top */}
              {job.candidates && job.candidates.length > candidatesPerPage && (
                <div className="flex items-center justify-between pb-4 mb-4 border-b border-white/[0.05]">
                  <p className="text-xs text-gray-400">
                    Showing {(currentPage - 1) * candidatesPerPage + 1}-
                    {Math.min(currentPage * candidatesPerPage, job.candidates.length)} of{' '}
                    {job.candidates.length}
                  </p>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className={`p-1.5 rounded transition-all ${
                        currentPage === 1
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 hover:text-white hover:bg-white/[0.05]'
                      }`}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>
                    <span className="text-sm text-gray-400">
                      {currentPage} / {Math.ceil(job.candidates.length / candidatesPerPage)}
                    </span>
                    <button
                      onClick={() =>
                        setCurrentPage(
                          Math.min(
                            Math.ceil(job.candidates.length / candidatesPerPage),
                            currentPage + 1
                          )
                        )
                      }
                      disabled={
                        currentPage === Math.ceil(job.candidates.length / candidatesPerPage)
                      }
                      className={`p-1.5 rounded transition-all ${
                        currentPage === Math.ceil(job.candidates.length / candidatesPerPage)
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 hover:text-white hover:bg-white/[0.05]'
                      }`}
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}

              <div className="flex-1 overflow-y-auto pr-2 custom-scrollbar space-y-3">
                {job.candidates && job.candidates.length > 0 ? (
                  job.candidates
                    .slice((currentPage - 1) * candidatesPerPage, currentPage * candidatesPerPage)
                    .map((candidate, index) => (
                      <motion.div
                        key={candidate.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <CulturalFitCard
                          candidate={candidate}
                          job={job}
                          questionLength={job.cultureFitQuestions?.length || 0}
                          onVideoModalOpen={() => {}}
                          onVideoModalClose={() => {}}
                        />
                      </motion.div>
                    ))
                ) : (
                  <div className="text-center py-12">
                    <div className="p-4 bg-gray-800/30 rounded-full inline-block mb-4">
                      <Video className="w-12 h-12 text-gray-500" />
                    </div>
                    <p className="text-gray-400 font-medium">No candidate responses yet</p>
                    <p className="text-gray-500 text-sm mt-2">
                      Responses will appear here as candidates submit their videos
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        <style jsx>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        `}</style>
      </div>
    </div>
  );
};

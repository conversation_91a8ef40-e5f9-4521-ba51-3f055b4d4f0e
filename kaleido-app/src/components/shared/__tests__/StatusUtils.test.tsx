import {
  getStatusInfo,
  jobSeekerHiringPath,
  commonHiringPath,
  getAvailableActions,
  convertApiStatus,
} from '../StatusUtils';
import { CandidateStatus } from '../../../types/candidate.types';

describe('StatusUtils', () => {
  describe('getStatusInfo', () => {
    test('returns correct info for all status types', () => {
      // Test a few key statuses
      const appliedInfo = getStatusInfo(CandidateStatus.APPLIED);
      expect(appliedInfo.label).toBe('Applied');
      expect(appliedInfo.color).toBe('teal');

      const offerExtendedInfo = getStatusInfo(CandidateStatus.OFFER_EXTENDED);
      expect(offerExtendedInfo.label).toBe('Offer Extended');
      expect(offerExtendedInfo.color).toBe('indigo');

      const offerApprovedInfo = getStatusInfo(CandidateStatus.OFFER_APPROVED);
      expect(offerApprovedInfo.label).toBe('Offer Approved');
      expect(offerApprovedInfo.color).toBe('green');
    });

    test('returns default info for unknown status', () => {
      // Cast to test edge case
      const unknownStatus = 'UNKNOWN_STATUS' as CandidateStatus;
      const info = getStatusInfo(unknownStatus);

      // Should return NEW status info as default
      expect(info.label).toBe('New');
      expect(info.color).toBe('gray');
    });
  });

  describe('jobSeekerHiringPath', () => {
    test('includes all expected statuses in correct order', () => {
      const expectedOrder = [
        CandidateStatus.APPLIED,
        CandidateStatus.MATCHED,
        CandidateStatus.SHORTLISTED,
        CandidateStatus.INTERVIEWING,
        CandidateStatus.OFFER_PENDING_APPROVAL,
        CandidateStatus.OFFER_APPROVED, // This was missing before our fix
        CandidateStatus.OFFER_EXTENDED,
        CandidateStatus.OFFER_ACCEPTED,
        CandidateStatus.HIRED,
      ];

      expect(jobSeekerHiringPath).toEqual(expectedOrder);
    });

    test('includes OFFER_APPROVED between OFFER_PENDING_APPROVAL and OFFER_EXTENDED', () => {
      const pendingIndex = jobSeekerHiringPath.indexOf(CandidateStatus.OFFER_PENDING_APPROVAL);
      const approvedIndex = jobSeekerHiringPath.indexOf(CandidateStatus.OFFER_APPROVED);
      const extendedIndex = jobSeekerHiringPath.indexOf(CandidateStatus.OFFER_EXTENDED);

      expect(pendingIndex).toBe(4);
      expect(approvedIndex).toBe(5);
      expect(extendedIndex).toBe(6);

      // Verify the order
      expect(approvedIndex).toBe(pendingIndex + 1);
      expect(extendedIndex).toBe(approvedIndex + 1);
    });

    test('has correct total number of statuses', () => {
      expect(jobSeekerHiringPath).toHaveLength(9);
    });

    test('starts with APPLIED status for job seekers', () => {
      expect(jobSeekerHiringPath[0]).toBe(CandidateStatus.APPLIED);
    });

    test('ends with HIRED status', () => {
      expect(jobSeekerHiringPath[jobSeekerHiringPath.length - 1]).toBe(CandidateStatus.HIRED);
    });
  });

  describe('commonHiringPath', () => {
    test('includes all expected statuses for employers', () => {
      const expectedOrder = [
        CandidateStatus.NEW,
        CandidateStatus.MATCHED,
        CandidateStatus.SHORTLISTED,
        CandidateStatus.INTERVIEWING,
        CandidateStatus.OFFER_PENDING_APPROVAL,
        CandidateStatus.OFFER_APPROVED,
        CandidateStatus.OFFER_EXTENDED,
        CandidateStatus.OFFER_ACCEPTED,
        CandidateStatus.HIRED,
      ];

      expect(commonHiringPath).toEqual(expectedOrder);
    });

    test('starts with NEW status for employers', () => {
      expect(commonHiringPath[0]).toBe(CandidateStatus.NEW);
    });
  });

  describe('Path Comparison', () => {
    test('jobSeekerHiringPath and commonHiringPath differ only in first status', () => {
      // Job seeker path starts with APPLIED, common path starts with NEW
      expect(jobSeekerHiringPath[0]).toBe(CandidateStatus.APPLIED);
      expect(commonHiringPath[0]).toBe(CandidateStatus.NEW);

      // Rest should be identical
      const jobSeekerTail = jobSeekerHiringPath.slice(1);
      const commonTail = commonHiringPath.slice(1);
      expect(jobSeekerTail).toEqual(commonTail);
    });

    test('both paths include complete offer flow', () => {
      const offerStatuses = [
        CandidateStatus.OFFER_PENDING_APPROVAL,
        CandidateStatus.OFFER_APPROVED,
        CandidateStatus.OFFER_EXTENDED,
        CandidateStatus.OFFER_ACCEPTED,
      ];

      offerStatuses.forEach(status => {
        expect(jobSeekerHiringPath).toContain(status);
        expect(commonHiringPath).toContain(status);
      });
    });
  });

  describe('getAvailableActions', () => {
    test('returns correct actions for each status', () => {
      const newActions = getAvailableActions(CandidateStatus.NEW);
      expect(newActions).toEqual([
        { status: CandidateStatus.SHORTLISTED, label: 'Shortlist Candidate' },
        { status: CandidateStatus.REJECTED, label: 'Reject' },
      ]);

      const interviewingActions = getAvailableActions(CandidateStatus.INTERVIEWING);
      expect(interviewingActions).toEqual([
        { status: CandidateStatus.OFFER_PENDING_APPROVAL, label: 'Create Offer' },
        { status: CandidateStatus.REJECTED, label: 'Reject' },
      ]);

      const offerApprovedActions = getAvailableActions(CandidateStatus.OFFER_APPROVED);
      expect(offerApprovedActions).toEqual([
        { status: CandidateStatus.OFFER_EXTENDED, label: 'Extend Offer' },
      ]);
    });

    test('returns empty array for terminal statuses', () => {
      const hiredActions = getAvailableActions(CandidateStatus.HIRED);
      expect(hiredActions).toEqual([]);

      const rejectedActions = getAvailableActions(CandidateStatus.REJECTED);
      expect(rejectedActions).toEqual([]);
    });
  });

  describe('convertApiStatus', () => {
    test('converts valid CandidateStatus values correctly', () => {
      expect(convertApiStatus('OFFER_EXTENDED')).toBe(CandidateStatus.OFFER_EXTENDED);
      expect(convertApiStatus('APPLIED')).toBe(CandidateStatus.APPLIED);
      expect(convertApiStatus('SHORTLISTED')).toBe(CandidateStatus.SHORTLISTED);
    });

    test('handles legacy API status mappings', () => {
      expect(convertApiStatus('OFFER_SENT')).toBe(CandidateStatus.OFFER_EXTENDED);
      expect(convertApiStatus('QUALIFIED')).toBe(CandidateStatus.SHORTLISTED);
    });

    test('defaults to APPLIED for unknown statuses', () => {
      expect(convertApiStatus('UNKNOWN_STATUS')).toBe(CandidateStatus.APPLIED);
      expect(convertApiStatus('')).toBe(CandidateStatus.APPLIED);
      expect(convertApiStatus('SOME_RANDOM_VALUE')).toBe(CandidateStatus.APPLIED);
    });

    test('logs warning for unknown statuses', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      convertApiStatus('UNKNOWN_STATUS');

      expect(consoleSpy).toHaveBeenCalledWith('Unknown status from API: UNKNOWN_STATUS');

      consoleSpy.mockRestore();
    });
  });

  describe('Status Integration Tests', () => {
    test('all statuses in paths have corresponding status info', () => {
      const allPathStatuses = [...new Set([...jobSeekerHiringPath, ...commonHiringPath])];

      allPathStatuses.forEach(status => {
        const info = getStatusInfo(status);
        expect(info).toBeDefined();
        expect(info.label).toBeTruthy();
        expect(info.color).toBeTruthy();
        expect(info.className).toBeTruthy();
        expect(info.icon).toBeDefined();
      });
    });

    test('OFFER_APPROVED is properly integrated', () => {
      // Verify OFFER_APPROVED exists in enum
      expect(CandidateStatus.OFFER_APPROVED).toBe('OFFER_APPROVED');

      // Verify it's in both paths
      expect(jobSeekerHiringPath).toContain(CandidateStatus.OFFER_APPROVED);
      expect(commonHiringPath).toContain(CandidateStatus.OFFER_APPROVED);

      // Verify it has status info
      const info = getStatusInfo(CandidateStatus.OFFER_APPROVED);
      expect(info.label).toBe('Offer Approved');
      expect(info.color).toBe('green');

      // Verify it has available actions (test with direct expectation)
      const actions = getAvailableActions(CandidateStatus.OFFER_APPROVED);
      expect(actions).toHaveLength(1);
      expect(actions[0]).toEqual({ status: CandidateStatus.OFFER_EXTENDED, label: 'Extend Offer' });
    });

    test('key status progressions work correctly', () => {
      // Test OFFER_APPROVED progression specifically
      const offerApprovedActions = getAvailableActions(CandidateStatus.OFFER_APPROVED);
      expect(offerApprovedActions).toEqual([
        { status: CandidateStatus.OFFER_EXTENDED, label: 'Extend Offer' },
      ]);

      // Test NEW status progression
      const newActions = getAvailableActions(CandidateStatus.NEW);
      expect(newActions).toContainEqual({
        status: CandidateStatus.SHORTLISTED,
        label: 'Shortlist Candidate',
      });

      // Test INTERVIEWING progression
      const interviewingActions = getAvailableActions(CandidateStatus.INTERVIEWING);
      expect(interviewingActions).toContainEqual({
        status: CandidateStatus.OFFER_PENDING_APPROVAL,
        label: 'Create Offer',
      });
    });
  });
});

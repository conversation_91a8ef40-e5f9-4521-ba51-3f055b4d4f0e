import React, { useEffect, useState } from 'react';

import { Award, Briefcase, Building, Clock, Download, Factory, Users } from 'lucide-react';

import RequiredFieldIndicator from '@/components/common/RequiredFieldIndicator';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import { Button } from '@/components/ui/button';
import { RadioCardGroup } from '@/components/ui/RadioCard';
// Import the SectionHeader component
import SectionHeader from '@/components/ui/SectionHeader';
import { DEPARTMENTS, JOB_TYPES } from '@/constants/jobOptions';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { Box, Typography } from '@mui/material';

import ImportJDModal from './ImportJDModal';
import StepLayout from './layout/StepLayout';

interface BasicJobInformationProps {
  requiredFields?: string[];
}

const BasicJobInformation: React.FC<BasicJobInformationProps> = ({ requiredFields = [] }) => {
  const { job, updateJobDescription, isLoading, isHydrated } = useJobs();
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [hasCheckedEditMode, setHasCheckedEditMode] = useState(false);

  // Check if we're in edit mode
  const urlParams =
    typeof window !== 'undefined'
      ? new URLSearchParams(window.location.search)
      : new URLSearchParams();
  const jobId = urlParams.get('jobId');
  const isEditMode = urlParams.get('edit') === 'true' && jobId;

  // Determine if data is loaded based on edit mode
  const isDataLoaded =
    !isEditMode ||
    (isEditMode &&
      (job.id === jobId || job.editingJobId === jobId || !!job.typeOfHiring || !!job.department)) ||
    !isHydrated;

  // Debug logging
  useEffect(() => {}, [isEditMode, jobId, job, isDataLoaded, isHydrated, isLoading]);

  // Check edit mode only once when component mounts
  useEffect(() => {
    if (!hasCheckedEditMode && isEditMode && isHydrated) {
      setHasCheckedEditMode(true);
    }
  }, [hasCheckedEditMode, isEditMode, isHydrated, jobId, job]);

  // Log job data changes separately
  useEffect(() => {
    if (job && Object.keys(job).length > 0) {
    }
  }, [job]);

  const handleDepartmentChange = (newValue: string) => {
    updateJobDescription('department', newValue);
    // Clear skills and responsibilities when department changes
    updateJobDescription('skills', []);
    updateJobDescription('jobResponsibilities', []);
  };

  const handleJobTypeChange = (newValue: string) => {
    updateJobDescription('jobType', newValue);
    updateJobDescription('skills', []);
    updateJobDescription('jobResponsibilities', []);
  };

  const handleHiringTypeChange = (value: string) => {
    updateJobDescription('typeOfHiring', value);
  };

  const handleJobArrangementChange = (value: string) => {
    updateJobDescription('typeOfJob', value);
  };

  // Experience handlers
  const handleExperienceChange = (value: string) => {
    updateJobDescription('experience', value);
  };

  const handleImportSuccess = () => {
    // The form will automatically update with the imported data
  };

  // Show loading state while waiting for job data in edit mode
  // Only show loader if we're truly loading - check if job has been populated
  const shouldShowLoader = isEditMode && !isHydrated;

  if (shouldShowLoader) {
    return (
      <StepLayout
        title="Basic Job Information"
        description="Tell us about the type of hiring, employment arrangement, department, and position type."
        icon={Briefcase}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-white/70">Loading job data...</p>
          </div>
        </div>
      </StepLayout>
    );
  }

  return (
    <StepLayout
      title="Basic Job Information"
      description="Tell us about the type of hiring, employment arrangement, department, and position type."
      icon={Briefcase}
    >
      <div className="space-y-6">
        {/* Import from Existing JD Section */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                <Download className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">
                  Import from Existing Job Description
                </h3>
                <p className="text-sm text-white/70">
                  Save time by importing an existing job description and let AI extract the
                  information
                </p>
              </div>
            </div>
            <Button
              onClick={() => setIsImportModalOpen(true)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700"
            >
              <Download className="w-4 h-4 mr-2" />
              Import JD
            </Button>
          </div>
        </div>

        {/* Hiring Information Section */}
        <div className="">
          <SectionHeader title="Hiring Information" icon={Briefcase} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Left Section - Hiring Type */}
            <div className="space-y-2">
              <p className="text-white/80 text-xs">What type of hiring is this?</p>
              <RadioCardGroup
                options={[
                  {
                    value: 'EMPLOYMENT',
                    label: 'Employment',
                    description: 'Regular hiring for your company',
                    icon: <Building size={16} />,
                  },
                  {
                    value: 'PROJECT',
                    label: 'Project',
                    description: 'Hiring for a specific project',
                    icon: <Briefcase size={16} />,
                  },
                ]}
                value={job.typeOfHiring || ''}
                onChange={handleHiringTypeChange}
                required={true}
              />
            </div>

            {/* Right Section - Job Type */}
            <div className="space-y-2">
              <p className="text-white/80 text-xs">
                What type of job arrangement are you offering?
              </p>
              <RadioCardGroup
                options={[
                  {
                    value: 'PERMANENT',
                    label: 'Permanent',
                    description: 'Full-time employment',
                    icon: <Users size={16} />,
                  },
                  {
                    value: 'CONTRACT',
                    label: 'Contract',
                    description: 'Fixed-term employment',
                    icon: <Briefcase size={16} />,
                  },
                  {
                    value: 'PART_TIME',
                    label: 'Part Time',
                    description: 'Reduced hours employment',
                    icon: <Clock size={16} />,
                  },
                ]}
                value={job.typeOfJob || ''}
                onChange={handleJobArrangementChange}
                required={true}
              />
            </div>
          </div>
        </div>

        {/* Department & Position Section */}
        <div>
          <SectionHeader title="Department & Position" icon={Factory} />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Left Section - Department */}
            <div>
              <StyledSelect
                label="Select the department for this position"
                options={DEPARTMENTS.map(department => ({ value: department, label: department }))}
                value={job.department || ''}
                onChange={handleDepartmentChange}
                placeholder="Select department"
                variant="dark"
                inputSize="sm"
                required={true}
              />
            </div>

            {/* Right Section - Position Type */}
            <div>
              <StyledSelect
                label="Select the type of position"
                options={JOB_TYPES.map(jobType => ({ value: jobType, label: jobType }))}
                value={job.jobType || ''}
                onChange={handleJobTypeChange}
                placeholder="Select job type"
                variant="dark"
                inputSize="sm"
                required={true}
              />
            </div>
          </div>
        </div>

        {/* Experience Level Section */}
        <div>
          <SectionHeader title="Experience Level" icon={Award} />
          <Box className="flex flex-col">
            <Typography variant="body2" className="mb-4 text-white/80 text-xs">
              Describe the experience level required for this position.{' '}
              <RequiredFieldIndicator variant="text" />
            </Typography>
            <div className="relative z-50 mt-2">
              <StyledSelect
                options={[
                  { value: 'Entry Level (0-2 years)', label: 'Entry Level (0-2 years)' },
                  { value: 'Mid Level (3-5 years)', label: 'Mid Level (3-5 years)' },
                  { value: 'Senior Level (5-8 years)', label: 'Senior Level (5-8 years)' },
                  { value: 'Expert Level (8+ years)', label: 'Expert Level (8+ years)' },
                ]}
                value={job.experience || ''}
                onChange={handleExperienceChange}
                placeholder="Select experience level..."
                allowCustomValues
                variant="dark"
                inputSize="sm"
                showListOnEmpty={true}
              />
            </div>
          </Box>
        </div>
      </div>

      {/* Import JD Modal */}
      {isImportModalOpen && (
        <ImportJDModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          onImportSuccess={handleImportSuccess}
        />
      )}
    </StepLayout>
  );
};

export default BasicJobInformation;

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';

import { showToast } from '@/components/Toaster';
import { ICandidate } from '@/entities/interfaces';
import { apiClient } from '@/lib/apiHelper';

interface VideoResponseModalProps {
  candidate: ICandidate;
  onClose: () => void;
  jobId?: string;
}

interface VideoResponse {
  id: string;
  question: string;
  videoUrl: string;
  duration: number;
  recordedAt: Date | string;
  questionId?: string;
  status?: string;
  isExpired?: boolean;
}

interface CultureFitQuestion {
  id: string;
  question: string;
  duration: number;
}

interface VideoResponseData {
  job: {
    id: string;
    jobType: string;
    companyName: string;
    department: string;
    cultureFitQuestions: CultureFitQuestion[];
  };
  candidate: {
    id: string;
    fullName: string;
    jobTitle: string;
    location: string;
    currentCompany: string;
    yearsOfExperience: number;
    videoResponses: VideoResponse[];
    answeredQuestions: number;
    totalQuestions: number;
    status: string;
  };
}

const VideoResponseModal: React.FC<VideoResponseModalProps> = ({ candidate, onClose, jobId }) => {
  const [videoResponses, setVideoResponses] = useState<VideoResponse[]>([]);
  const [cultureFitQuestions, setCultureFitQuestions] = useState<CultureFitQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedResponse, setSelectedResponse] = useState<VideoResponse | null>(null);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Format date with full date and time
  const formatDate = (date: Date | string) => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format duration to minutes:seconds
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get relative time
  const getRelativeTime = (date: Date | string) => {
    const dateObj = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  // Listen for culture fit updates
  useEffect(() => {
    const handleCultureFitUpdate = () => {
      // Trigger a refresh by updating the refresh key
      setRefreshKey(prev => prev + 1);
    };

    // Listen for a custom event that can be triggered after culture fit update
    window.addEventListener('cultureFitUpdated', handleCultureFitUpdate);

    return () => {
      window.removeEventListener('cultureFitUpdated', handleCultureFitUpdate);
    };
  }, []);

  // Fetch video responses from the API
  useEffect(() => {
    const fetchVideoResponses = async () => {
      // If we have jobId and candidate.id, fetch from the new endpoint
      if (jobId && candidate.id) {
        setIsLoading(true);
        try {
          const response = await apiClient.get<VideoResponseData>(
            `/jobs/${jobId}/candidates/${candidate.id}/video-responses`
          );

          // Set culture fit questions
          if (response?.job?.cultureFitQuestions) {
            setCultureFitQuestions(response.job.cultureFitQuestions);
          }

          if (response?.candidate?.videoResponses && response.candidate.videoResponses.length > 0) {
            setVideoResponses(response.candidate.videoResponses);
            // Find the first question that has a response and select it
            for (const question of response.job.cultureFitQuestions || []) {
              const matchingResponse = response.candidate.videoResponses.find(
                r => r.question === question.question
              );
              if (matchingResponse) {
                setSelectedResponse(matchingResponse);
                break;
              }
            }
            // If no matching response found, just select the first video response
            if (!selectedResponse && response.candidate.videoResponses.length > 0) {
              setSelectedResponse(response.candidate.videoResponses[0]);
            }
          } else if (response?.job?.cultureFitQuestions?.length > 0) {
            // No video responses but we have questions
            setVideoResponses([]);
            setSelectedQuestionId(response.job.cultureFitQuestions[0].id);
          }
        } catch (error) {
          console.error('Error fetching video responses:', error);
          showToast({ message: 'Failed to load video responses', type: 'error' });
          // Fallback to using candidate's existing video responses
          if (candidate.videoResponses) {
            const uniqueResponses = getUniqueResponses(candidate.videoResponses);
            setVideoResponses(uniqueResponses);
            if (uniqueResponses.length > 0) {
              setSelectedResponse(uniqueResponses[0]);
            }
          }
        } finally {
          setIsLoading(false);
        }
      } else if (candidate.videoResponses) {
        // If no jobId, use candidate's existing video responses
        const uniqueResponses = getUniqueResponses(candidate.videoResponses);
        setVideoResponses(uniqueResponses);
        if (uniqueResponses.length > 0) {
          setSelectedResponse(uniqueResponses[0]);
        }
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    };

    fetchVideoResponses();
  }, [candidate.id, jobId, refreshKey]);

  // Helper function to get unique responses
  const getUniqueResponses = (responses: VideoResponse[]): VideoResponse[] => {
    const uniqueMap = responses.reduce(
      (acc, response) => {
        if (
          !acc[response.question] ||
          new Date(response.recordedAt).getTime() >
            new Date(acc[response.question].recordedAt).getTime()
        ) {
          acc[response.question] = response;
        }
        return acc;
      },
      {} as Record<string, VideoResponse>
    );

    // Convert to array and sort by recordedAt
    const sorted = Object.values(uniqueMap);
    sorted.sort((a, b) => new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime());
    return sorted;
  };

  if (isLoading) {
    return createPortal(
      <div className="fixed inset-0 z-[10001] bg-black/50 backdrop-blur-md flex items-center justify-center">
        <div className="w-full max-w-7xl mx-auto p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Questions List Skeleton */}
            <div className="col-span-1 space-y-3">
              <div className="h-6 w-32 bg-muted/50 rounded animate-pulse mb-4" />
              {[1, 2, 3].map(i => (
                <div key={i} className="rounded-xl p-4 bg-muted/30 animate-pulse">
                  <div className="h-4 bg-muted/50 rounded w-full mb-3" />
                  <div className="h-4 bg-muted/50 rounded w-3/4 mb-3" />
                  <div className="flex items-center gap-3">
                    <div className="h-3 bg-muted/50 rounded w-20" />
                    <div className="h-3 bg-muted/50 rounded w-24" />
                  </div>
                </div>
              ))}
            </div>
            {/* Video Player Skeleton */}
            <div className="col-span-2">
              <div className="rounded-xl p-6 bg-muted/30 animate-pulse">
                <div className="h-6 bg-muted/50 rounded w-3/4 mb-4" />
                <div className="aspect-video bg-black/50 rounded-lg mb-4" />
                <div className="flex items-center gap-4">
                  <div className="h-4 bg-muted/50 rounded w-32" />
                  <div className="h-4 bg-muted/50 rounded w-40" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>,
      document.body
    );
  }

  if (!videoResponses || videoResponses.length === 0) {
    // If we have culture fit questions but no responses, show the questions
    if (cultureFitQuestions.length > 0) {
      // Continue to render the main component with empty responses
    } else {
      // No questions and no responses - show empty state
      return createPortal(
        <div className="fixed inset-0 z-[10001] bg-black/50 backdrop-blur-md flex items-center justify-center">
          <div className="relative bg-white/90 dark:bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 max-w-md mx-auto border border-gray-300/20 dark:border-white/10 shadow-2xl">
            <button
              type="button"
              onClick={onClose}
              className="absolute top-4 right-4 p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
              aria-label="Close"
            >
              <X className="w-6 h-6 text-gray-600 dark:text-gray-400" />
            </button>
            <EmptyState
              title="No Video Responses"
              description="This candidate hasn't submitted any video responses yet. Video introductions help you better understand the candidate's communication skills and personality."
              icon={Video}
              type="generic"
              showButton={false}
            />
          </div>
        </div>,
        document.body
      );
    }
  }

  return createPortal(
    <div className="fixed inset-0 z-[10001] bg-black/50 backdrop-blur-md">
      {/* Modal Container */}
      <div className="fixed inset-4 bg-white/90 dark:bg-gray-800/50 backdrop-blur-xl rounded-2xl overflow-hidden border border-gray-300/20 dark:border-white/10 shadow-2xl">
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 p-6 border-b border-gray-300/20 dark:border-white/10 backdrop-blur-md z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Video className="w-6 h-6 text-muted-foreground" />
              <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                  Video Introduction Responses
                </h2>
                <p className="text-gray-500 dark:text-gray-400">{candidate.fullName}</p>
              </div>
            </div>
            <button
              type="button"
              onClick={onClose}
              className="p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
              aria-label="Close video responses"
            >
              <X className="w-6 h-6 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="pt-24 h-full">
          <div className="w-full max-w-7xl mx-auto h-full">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 h-full p-6">
              {/* Questions Sidebar */}
              <div className="col-span-1 lg:col-span-2">
                <div className="sticky top-0">
                  {/* Header */}
                  <div className="mb-6 flex">
                    <Video className="w-6 h-6 text-muted-foreground mr-3" />
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-muted-foreground tracking-wider">
                        Video Intro Response
                      </h3>
                      <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                        {cultureFitQuestions.length > 0
                          ? cultureFitQuestions.length
                          : videoResponses.length}{' '}
                        Total
                      </span>
                    </div>
                    <div className="h-px bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent" />
                  </div>

                  {/* Questions List */}
                  <div className="space-y-2">
                    <AnimatePresence>
                      {/* Show all culture fit questions with their responses */}
                      {cultureFitQuestions.map((question: CultureFitQuestion, index: number) => {
                        // Match by question text since questionId might not match
                        const response = videoResponses.find(r => r.question === question.question);
                        const isSelected = response
                          ? selectedResponse?.id === response.id
                          : selectedQuestionId === question.id;

                        return (
                          <motion.button
                            key={question.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            onClick={() => {
                              if (response) {
                                setSelectedResponse(response);
                                setSelectedQuestionId(null);
                              } else {
                                setSelectedResponse(null);
                                setSelectedQuestionId(question.id);
                              }
                            }}
                            className={`w-full text-left transition-all duration-300 group relative ${
                              isSelected ? 'scale-[1.02]' : 'hover:scale-[1.01]'
                            }`}
                          >
                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                            <div
                              className={`relative flex gap-4 p-4 rounded-2xl border transition-all duration-300 ${
                                isSelected ? '' : 'hover:bg-white/5 border-transparent'
                              }`}
                            >
                              {/* Glassmorphic gradient background for selected item */}
                              {isSelected && (
                                <>
                                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-purple-600/20 blur-xl" />
                                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
                                  <div className="absolute inset-0 rounded-2xl backdrop-blur-sm" />
                                  <div className="absolute inset-0 rounded-2xl border border-purple-400/30" />
                                </>
                              )}
                              {/* Question Number */}
                              <div className="relative flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center font-bold transition-all duration-300 z-10">
                                {isSelected ? (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg shadow-purple-500/30" />
                                    <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/20 to-transparent" />
                                    <span className="relative text-white">
                                      {(index + 1).toString().padStart(2, '0')}
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-muted/50" />
                                    <span className="relative text-muted-foreground group-hover:text-foreground">
                                      {(index + 1).toString().padStart(2, '0')}
                                    </span>
                                  </>
                                )}
                              </div>

                              {/* Question Content */}
                              <div className="flex-1 min-w-0 relative z-10">
                                <p
                                  className={`font-medium line-clamp-2 mb-2 transition-colors duration-300 ${
                                    isSelected
                                      ? 'text-foreground'
                                      : 'text-muted-foreground group-hover:text-foreground'
                                  }`}
                                >
                                  {question.question}
                                </p>

                                <div className="flex items-center gap-3 text-xs">
                                  {response ? (
                                    <>
                                      <div
                                        className={`flex items-center gap-1 transition-colors duration-300 ${
                                          isSelected
                                            ? 'text-purple-400'
                                            : 'text-muted-foreground/70'
                                        }`}
                                      >
                                        <Timer className="w-3 h-3" />
                                        <span>{formatDuration(response.duration)}</span>
                                      </div>
                                      <span className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
                                      <span
                                        className={`transition-colors duration-300 ${
                                          isSelected ? 'text-pink-400' : 'text-muted-foreground/70'
                                        }`}
                                      >
                                        {getRelativeTime(response.recordedAt)}
                                      </span>
                                    </>
                                  ) : (
                                    <span className="text-muted-foreground/70 italic">
                                      Candidate has not responded yet
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* Play Icon or Status Icon */}
                              <div className="relative flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 z-10">
                                {response ? (
                                  isSelected ? (
                                    <>
                                      {/* Glassmorphic play button */}
                                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-500/30 to-pink-500/30 backdrop-blur-sm" />
                                      <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/10 to-transparent" />
                                      <div className="absolute inset-0 rounded-full border border-purple-400/20" />
                                      <Play
                                        className="relative w-4 h-4 text-purple-300"
                                        fill="currentColor"
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <div className="absolute inset-0 rounded-full bg-muted/30 group-hover:bg-gradient-to-br group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-300" />
                                      <Play
                                        className="relative w-4 h-4 text-muted-foreground group-hover:text-purple-400 transition-colors duration-300"
                                        fill="currentColor"
                                      />
                                    </>
                                  )
                                ) : (
                                  <>
                                    <div className="absolute inset-0 rounded-full bg-muted/20" />
                                    <Timer className="relative w-4 h-4 text-muted-foreground/50" />
                                  </>
                                )}
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                </div>
              </div>

              {/* Video Player */}
              <div className="col-span-1 lg:col-span-3">
                <AnimatePresence mode="wait">
                  {selectedResponse ? (
                    <motion.div
                      key={selectedResponse.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                      className="h-full"
                    >
                      <div className="relative h-full rounded-3xl overflow-hidden">
                        {/* Sophisticated Gradient Background */}
                        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-600/5" />
                        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--primary)/10,_transparent_40%)]" />
                        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_var(--purple-600)/10,_transparent_40%)]" />

                        <div className="relative h-full p-8 backdrop-blur-sm bg-white/5 dark:bg-black/5 border border-white/10">
                          {/* Video Container */}
                          <div className="h-full flex flex-col justify-center">
                            {/* Video Player with Enhanced Shadow */}
                            <div className="relative w-full max-w-4xl mx-auto rounded-2xl overflow-hidden bg-black shadow-2xl ring-1 ring-white/10">
                              <div className="aspect-video">
                                <video
                                  key={selectedResponse.id}
                                  src={selectedResponse.videoUrl}
                                  controls
                                  controlsList="nodownload"
                                  className="w-full h-full object-contain"
                                  playsInline
                                />
                              </div>

                              {/* Floating Info Overlay */}
                              <div className="absolute top-6 left-6 right-6 flex justify-between items-start pointer-events-none">
                                {/* Question Number Badge */}
                                <div className="bg-black/60 backdrop-blur-md px-4 py-2 rounded-full flex items-center gap-2">
                                  <span className="text-white/70 text-sm">Question</span>
                                  <span className="text-white font-bold">
                                    {videoResponses.findIndex(r => r.id === selectedResponse.id) +
                                      1}
                                  </span>
                                  <span className="text-white/70 text-sm">
                                    of {videoResponses.length}
                                  </span>
                                </div>

                                {/* Duration Badge */}
                                <div className="bg-black/60 backdrop-blur-md px-3 py-2 rounded-full text-white text-sm flex items-center gap-2">
                                  <Timer className="w-4 h-4" />
                                  <span className="font-medium">
                                    {formatDuration(selectedResponse.duration)}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Elegant Info Bar */}
                            <div className="mt-6 flex items-center justify-between px-2 max-w-4xl mx-auto w-full">
                              {/* Status */}
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                                  <CheckCircle className="w-4 h-4 text-green-500" />
                                </div>
                                <div>
                                  <p className="text-xs text-muted-foreground">Status</p>
                                  <p className="text-sm font-medium text-green-600 dark:text-green-400">
                                    Completed
                                  </p>
                                </div>
                              </div>

                              {/* Navigation Hints */}
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                                  <Calendar className="w-4 h-4 text-primary" />
                                </div>
                                <div>
                                  <p className="text-xs text-muted-foreground">Recorded</p>
                                  <p className="text-[8pt] font-sm">
                                    {formatDate(selectedResponse.recordedAt)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ) : selectedQuestionId ? (
                    // Show the selected question with no response
                    <motion.div
                      key={selectedQuestionId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                      className="h-full"
                    >
                      <div className="h-full flex justify-center items-center">
                        <div className="relative p-8 rounded-2xl overflow-hidden border border-white/10 backdrop-blur-sm">
                          <motion.div
                            animate={{
                              rotate: [0, 360],
                              scale: [1, 1.1, 1],
                            }}
                            transition={{
                              duration: 15,
                              repeat: Infinity,
                              ease: 'linear',
                            }}
                            className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 blur-2xl"
                          />
                          <motion.div
                            animate={{
                              x: [-40, 40, -40],
                              y: [-20, 20, -20],
                            }}
                            transition={{
                              duration: 8,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                            className="absolute inset-0 bg-gradient-to-tr from-white/5 via-white/10 to-transparent blur-xl"
                          />

                          <div className="relative p-6">
                            <motion.div
                              initial={{ opacity: 0, scale: 0.95 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{
                                duration: 0.3,
                                ease: 'easeOut',
                              }}
                              className="flex justify-center mb-6 relative"
                            >
                              <motion.div
                                animate={{
                                  opacity: [0.4, 0.8, 0.4],
                                  scale: [0.95, 1.05, 0.95],
                                }}
                                transition={{
                                  duration: 4,
                                  repeat: Infinity,
                                  ease: 'easeInOut',
                                }}
                                className="absolute inset-0 bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-pink-500/30 rounded-full blur-xl"
                              />
                              <div className="relative">
                                <motion.div
                                  animate={{
                                    opacity: [0.5, 1, 0.5],
                                  }}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: 'easeInOut',
                                  }}
                                  className="absolute inset-0 bg-gradient-to-tr from-indigo-500 to-purple-500 rounded-full blur-md"
                                />
                                <Timer className="w-16 h-16 text-white relative z-10" />
                              </div>
                            </motion.div>

                            <motion.div
                              initial={{ opacity: 0, y: 5 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.1 }}
                              className="text-center relative z-10"
                            >
                              <h3 className="text-xl font-semibold text-white mb-3">
                                Awaiting Response
                              </h3>
                              <p className="text-white/60 max-w-md mx-auto leading-relaxed text-sm">
                                The candidate has not responded to this question yet. You'll be able
                                to view their video response once they submit it.
                              </p>
                              <div className="mt-4 text-white/40 text-xs">
                                Expected duration:{' '}
                                {
                                  cultureFitQuestions.find(q => q.id === selectedQuestionId)
                                    ?.duration
                                }{' '}
                                min
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="h-full flex items-center justify-center min-h-[500px]"
                    >
                      <div className="text-center">
                        <div className="relative inline-flex">
                          <div className="absolute inset-0 bg-primary/20 blur-xl rounded-full animate-pulse" />
                          <div className="relative p-6 rounded-full bg-gradient-to-br from-primary/10 to-purple-600/10 border border-white/10">
                            <FileVideo className="w-12 h-12 text-primary" />
                          </div>
                        </div>
                        <h3 className="mt-6 text-xl font-semibold mb-2">Select a Video Response</h3>
                        <p className="text-muted-foreground max-w-sm mx-auto">
                          Choose a question from the sidebar to view the candidate's video response
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default VideoResponseModal;

import { useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { Calendar, Clock, Download, ExternalLink, Info, Play, User, Video, X } from 'lucide-react';

import { ICandidate } from '@/entities/interfaces';

interface VideoResponseModalProps {
  candidate: ICandidate;
  onClose: () => void;
}

const VideoResponseModal: React.FC<VideoResponseModalProps> = ({ candidate, onClose }) => {
  const [selectedVideoIndex, setSelectedVideoIndex] = useState(0);

  // Ensure we have video responses
  if (!candidate.videoResponses || candidate.videoResponses.length === 0) {
    return null;
  }

  return createPortal(
    <div
      className="fixed inset-0 z-[10001] bg-white dark:bg-black"
      style={{ backgroundColor: `var(--background-rgb)` }}
    >
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 dark:bg-gray-900/60 backdrop-blur-md" />

      <AnimatePresence mode="wait">
        <motion.div
          className={`fixed ${
            isMobile ? 'inset-0' : 'inset-4'
          } bg-white/90 dark:bg-gray-800/50 backdrop-blur-xl ${
            isMobile ? 'rounded-none' : 'rounded-2xl'
          } overflow-hidden border border-gray-300/20 dark:border-white/10 shadow-2xl`}
          style={{
            backgroundColor: `var(--card-bg)`,
            borderColor: `var(--card-border)`,
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          {/* Header */}
          <div
            className="absolute top-0 left-0 right-0 p-6 border-b border-gray-300/20 dark:border-white/10 backdrop-blur-md"
            style={{ borderColor: `var(--card-border)` }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {isMobile && showQuestionsList && (
                  <button
                    type="button"
                    onClick={() => setShowQuestionsList(false)}
                    className="p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
                    aria-label="Back to video"
                  >
                    <ChevronLeft
                      className="w-6 h-6 text-gray-600 dark:text-gray-400"
                      style={{ color: `var(--input-placeholder)` }}
                    />
                  </button>
                )}
                <div>
                  <h2
                    className="text-xl font-semibold text-gray-800 dark:text-white"
                    style={{ color: `var(--foreground-color)` }}
                  >
                    {isMobile && showQuestionsList
                      ? 'Select Question'
                      : 'Video Introduction Responses'}
                  </h2>
                  <p
                    className="text-gray-500 dark:text-gray-400"
                    style={{ color: `var(--input-placeholder)` }}
                  >
                    {candidate.fullName}
                  </p>
                </div>
              </div>
              <button
                type="button"
                onClick={onClose}
                className="p-2 hover:bg-gray-200/50 dark:hover:bg-white/10 rounded-full transition-colors"
                aria-label="Close video responses"
              >
                <X
                  className="w-6 h-6 text-gray-600 dark:text-gray-400"
                  style={{ color: `var(--input-placeholder)` }}
                />
              </button>
            </div>
          </div>

          {/* Content */}
          <div
            className={`${isMobile ? 'flex flex-col' : 'flex'} h-full ${isMobile ? 'pt-16' : 'pt-24'}`}
          >
            {/* Questions List */}
            <div
              className={`${
                isMobile ? (showQuestionsList ? 'w-full h-full' : 'hidden') : 'w-1/3'
              } p-6 ${isMobile ? '' : 'border-r'} border-gray-300/20 dark:border-white/10 overflow-y-auto`}
              style={{ borderColor: `var(--card-border)` }}
            >
              <div className="space-y-4">
                {candidateResponses.map((response: IVideoResponse) => (
                  <button
                    type="button"
                    key={response.id}
                    onClick={() => {
                      setSelectedResponse(response);
                      if (isMobile) {
                        setShowQuestionsList(false);
                      }
                    }}
                    className={`w-full p-4 rounded-xl text-left transition-all ${
                      selectedResponse?.id === response.id
                        ? 'bg-gray-200/70 dark:bg-white/20 shadow-lg'
                        : 'bg-gray-100/50 hover:bg-gray-200/50 dark:bg-white/5 dark:hover:bg-white/10'
                    }`}
                    style={{
                      backgroundColor:
                        selectedResponse?.id === response.id
                          ? currentTheme === 'light'
                            ? 'var(--button-secondary-bg)'
                            : 'var(--card-bg)'
                          : 'transparent',
                      boxShadow:
                        selectedResponse?.id === response.id ? 'var(--card-shadow)' : 'none',
                    }}
                    aria-label={`View response for: ${response.question}`}
                  >
                    <p
                      className="text-gray-800 dark:text-white font-medium line-clamp-2"
                      style={{ color: `var(--foreground-color)` }}
                    >
                      {response.question}
                    </p>
                    <div
                      className="flex items-center gap-4 text-gray-500 dark:text-gray-400 text-sm mt-2"
                      style={{ color: `var(--input-placeholder)` }}
                    >
                      <p>Duration: {response.duration}s</p>
                      <p>Recorded: {new Date(response.recordedAt).toLocaleString()}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Video Player */}
            <div
              className={`${
                isMobile ? (showQuestionsList ? 'hidden' : 'flex-1') : 'flex-1'
              } p-6 overflow-y-auto`}
            >
              {selectedResponse ? (
                <div className="space-y-6">
                  {/* Mobile Questions Toggle Button */}
                  {isMobile && (
                    <button
                      type="button"
                      onClick={() => setShowQuestionsList(true)}
                      className="w-full p-3 bg-gray-100/50 hover:bg-gray-200/50 dark:bg-white/5 dark:hover:bg-white/10 rounded-lg transition-colors flex items-center justify-center gap-2"
                      aria-label="View all questions"
                    >
                      <List className="w-5 h-5" />
                      <span>View All Questions ({candidateResponses.length})</span>
                    </button>
                  )}

                  <div
                    className="bg-gray-100/70 dark:bg-white/5 rounded-xl p-6"
                    style={{
                      backgroundColor:
                        currentTheme === 'light' ? 'var(--card-bg)' : 'var(--card-bg)',
                    }}
                  >
                    <h3
                      className="text-lg font-medium text-gray-800 dark:text-white mb-4"
                      style={{ color: `var(--foreground-color)` }}
                    >
                      {selectedResponse.question}
                    </h3>
                    <div
                      className={`${isMobile ? 'aspect-[9/16]' : 'aspect-video'} rounded-lg overflow-hidden bg-gray-200/50 dark:bg-black/50`}
                    >
                      <video
                        key={selectedResponse.id}
                        src={selectedResponse.videoUrl}
                        controls
                        controlsList="nodownload"
                        className={`w-full h-full ${isMobile ? 'object-cover' : 'object-contain'}`}
                        playsInline
                      />
                    </div>
                    <div
                      className="mt-4 text-sm text-gray-500 dark:text-gray-400"
                      style={{ color: `var(--input-placeholder)` }}
                    >
                      <p>Duration: {selectedResponse.duration} seconds</p>
                      <p>Recorded on: {new Date(selectedResponse.recordedAt).toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400"
                  style={{ color: `var(--input-placeholder)` }}
                >
                  Select a response to view
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>,
    document.body
  );
};
export default VideoResponseModal;
